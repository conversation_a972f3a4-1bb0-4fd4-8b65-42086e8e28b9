import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shivish/shared/services/payment/payment_service_provider.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class PaymentDashboardScreen extends ConsumerStatefulWidget {
  const PaymentDashboardScreen({super.key});

  @override
  ConsumerState<PaymentDashboardScreen> createState() =>
      _PaymentDashboardScreenState();
}

class _PaymentDashboardScreenState
    extends ConsumerState<PaymentDashboardScreen> {
  bool _isLoading = true;
  String? _error;
  double _totalEarnings = 0;
  double _pendingSettlements = 0;
  double _totalSettlements = 0;
  List<Map<String, dynamic>> _recentTransactions = [];
  List<Map<String, dynamic>> _earningsData = [];

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    try {
      final paymentService = ref.read(paymentServiceProvider);
      final dashboardData = await paymentService.getDashboardData();

      if (mounted) {
        setState(() {
          _totalEarnings = dashboardData['totalEarnings'] ?? 0;
          _pendingSettlements = dashboardData['pendingSettlements'] ?? 0;
          _totalSettlements = dashboardData['totalSettlements'] ?? 0;
          _recentTransactions = List<Map<String, dynamic>>.from(
            dashboardData['recentTransactions'] ?? [],
          );
          _earningsData = List<Map<String, dynamic>>.from(
            dashboardData['earningsData'] ?? [],
          );
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: LoadingIndicator(),
      );
    }

    if (_error != null) {
      return Scaffold(
        body: ErrorMessage(
          message: _error!,
          onRetry: _loadDashboardData,
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Payment Dashboard'),
      ),
      body: RefreshIndicator(
        onRefresh: _loadDashboardData,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCards(),
              const SizedBox(height: 24),
              _buildEarningsChart(),
              const SizedBox(height: 24),
              _buildRecentTransactions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildSummaryCard(
          'Total Earnings',
          '₹${_totalEarnings.toStringAsFixed(2)}',
          Icons.currency_rupee,
          Colors.green,
        ),
        _buildSummaryCard(
          'Pending Settlements',
          '₹${_pendingSettlements.toStringAsFixed(2)}',
          Icons.pending,
          Colors.orange,
        ),
        _buildSummaryCard(
          'Total Settlements',
          '₹${_totalSettlements.toStringAsFixed(2)}',
          Icons.check_circle,
          Colors.blue,
        ),
        _buildSummaryCard(
          'Commission Rate',
          '10%',
          Icons.percent,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 32,
              color: color,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsChart() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Earnings Overview',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: FlTitlesData(
                    leftTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        getTitlesWidget: (value, meta) {
                          if (value.toInt() >= _earningsData.length) {
                            return const SizedBox.shrink();
                          }
                          return Text(
                            _earningsData[value.toInt()]['date'],
                            style: Theme.of(context).textTheme.bodySmall,
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _earningsData.asMap().entries.map((entry) {
                        return FlSpot(
                          entry.key.toDouble(),
                          entry.value['amount'].toDouble(),
                        );
                      }).toList(),
                      isCurved: true,
                      color: Theme.of(context).colorScheme.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentTransactions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Transactions',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (_recentTransactions.isEmpty)
              Center(
                child: Text(
                  'No recent transactions',
                  style: Theme.of(context).textTheme.bodyLarge,
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _recentTransactions.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final transaction = _recentTransactions[index];
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: Theme.of(context)
                          .colorScheme
                          .primary
                          .withOpacity(0.1),
                      child: Icon(
                        transaction['type'] == 'settlement'
                            ? Icons.payment
                            : Icons.shopping_cart,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    title: Text(transaction['description']),
                    subtitle: Text(transaction['date']),
                    trailing: Text(
                      '₹${transaction['amount'].toStringAsFixed(2)}',
                      style: TextStyle(
                        color: transaction['amount'] >= 0
                            ? Colors.green
                            : Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}

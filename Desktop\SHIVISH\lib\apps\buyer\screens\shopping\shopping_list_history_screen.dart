import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../../../shared/providers/auth_provider.dart';

/// Screen to display the history of shopping lists
class ShoppingListHistoryScreen extends ConsumerStatefulWidget {
  /// Creates a [ShoppingListHistoryScreen]
  const ShoppingListHistoryScreen({super.key});

  @override
  ConsumerState<ShoppingListHistoryScreen> createState() =>
      _ShoppingListHistoryScreenState();
}

class _ShoppingListHistoryScreenState
    extends ConsumerState<ShoppingListHistoryScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider).value;

    // User will always be signed in when accessing this screen
    final completedListsAsync =
        ref.watch(userCompletedShoppingListsProvider(user?.uid ?? ''));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Shopping History'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: completedListsAsync.when(
        data: (lists) {
          if (lists.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.history,
                    size: 64,
                    color: theme.colorScheme.primary.withAlpha(128),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Shopping History',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your completed shopping lists will appear here',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          // Sort lists by date (newest first)
          final sortedLists = List<ShoppingListModel>.from(lists);
          sortedLists.sort((a, b) => b.updatedAt.compareTo(a.updatedAt));

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: sortedLists.length,
            itemBuilder: (context, index) {
              final list = sortedLists[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: ListTile(
                  title: Text(list.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Completed on: ${_formatDate(list.updatedAt)}',
                        style: theme.textTheme.bodySmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${list.itemCount} items • ₹${list.totalPrice.toStringAsFixed(2)}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (list.selectedSellerName != null)
                        Text(
                          'Seller: ${list.selectedSellerName}',
                          style: theme.textTheme.bodySmall,
                        ),
                    ],
                  ),
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check_circle,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  trailing: PopupMenuButton<String>(
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: Text('View Details'),
                      ),
                      const PopupMenuItem(
                        value: 'reuse',
                        child: Text('Reuse List'),
                      ),
                    ],
                    onSelected: (value) async {
                      if (value == 'view') {
                        // Navigate to list details
                        // This would typically use a route
                        // For now, we'll just show a dialog
                        if (mounted) {
                          showDialog<void>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(list.name),
                              content: SizedBox(
                                width: double.maxFinite,
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: list.items.length,
                                  itemBuilder: (context, index) {
                                    final item = list.items[index];
                                    return ListTile(
                                      title: Text(item.name),
                                      subtitle: Text(
                                        'Qty: ${item.quantity} • ₹${item.price.toStringAsFixed(2)}',
                                      ),
                                      leading: Checkbox(
                                        value: item.isChecked,
                                        onChanged: null,
                                      ),
                                    );
                                  },
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('CLOSE'),
                                ),
                              ],
                            ),
                          );
                        }
                      } else if (value == 'reuse') {
                        // Create a new list based on this one
                        try {
                          final newList = list.copyWith(
                            id: const Uuid().v4(),
                            name: '${list.name} (Copy)',
                            status: 'pending',
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                            selectedSellerId: null,
                            selectedSellerName: null,
                            items: list.items.map((item) {
                              return item.copyWith(
                                isChecked: false,
                              );
                            }).toList(),
                          );

                          await ref
                              .read(shoppingListServiceProvider)
                              .createShoppingList(newList);

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('List copied successfully'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Failed to copy list: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      }
                    },
                  ),
                ),
              );
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error loading shopping history: $error'),
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Provider for completed shopping lists
final userCompletedShoppingListsProvider =
    StreamProvider.family<List<ShoppingListModel>, String>((ref, userId) {
  return ref
      .watch(shoppingListServiceProvider)
      .getCompletedShoppingLists(userId);
});

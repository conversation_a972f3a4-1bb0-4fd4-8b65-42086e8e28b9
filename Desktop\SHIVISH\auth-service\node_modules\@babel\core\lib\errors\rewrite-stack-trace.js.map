{"version": 3, "names": ["ErrorToString", "Function", "call", "bind", "Error", "prototype", "toString", "SUPPORTED", "captureStackTrace", "_Object$getOwnPropert", "Object", "getOwnPropertyDescriptor", "writable", "START_HIDING", "STOP_HIDING", "expectedErrors", "WeakSet", "virtualFrames", "WeakMap", "CallSite", "filename", "create", "isNative", "isConstructor", "isToplevel", "getFileName", "getLineNumber", "undefined", "getColumnNumber", "getFunctionName", "getMethodName", "getTypeName", "injectVirtualStackFrame", "error", "frames", "get", "set", "push", "expectedError", "add", "beginHiddenCallStack", "fn", "defineProperty", "args", "setupPrepareStackTrace", "value", "endHiddenCallStack", "prepareStackTrace", "defaultPrepareStackTrace", "MIN_STACK_TRACE_LIMIT", "stackTraceLimit", "Math", "max", "stackTraceRewriter", "err", "trace", "newTrace", "isExpected", "has", "status", "i", "length", "name", "unshift", "join"], "sources": ["../../src/errors/rewrite-stack-trace.ts"], "sourcesContent": ["/**\n * This file uses the internal V8 Stack Trace API (https://v8.dev/docs/stack-trace-api)\n * to provide utilities to rewrite the stack trace.\n * When this API is not present, all the functions in this file become noops.\n *\n * beginHiddenCallStack(fn) and endHiddenCallStack(fn) wrap their parameter to\n * mark an hidden portion of the stack trace. The function passed to\n * beginHiddenCallStack is the first hidden function, while the function passed\n * to endHiddenCallStack is the first shown function.\n *\n * When an error is thrown _outside_ of the hidden zone, everything between\n * beginHiddenCallStack and endHiddenCallStack will not be shown.\n * If an error is thrown _inside_ the hidden zone, then the whole stack trace\n * will be visible: this is to avoid hiding real bugs.\n * However, if an error inside the hidden zone is expected, it can be marked\n * with the expectedError(error) function to keep the hidden frames hidden.\n *\n * Consider this call stack (the outer function is the bottom one):\n *\n *   1. a()\n *   2. endHiddenCallStack(b)()\n *   3. c()\n *   4. beginHiddenCallStack(d)()\n *   5. e()\n *   6. f()\n *\n * - If a() throws an error, then its shown call stack will be \"a, b, e, f\"\n * - If b() throws an error, then its shown call stack will be \"b, e, f\"\n * - If c() throws an expected error, then its shown call stack will be \"e, f\"\n * - If c() throws an unexpected error, then its shown call stack will be \"c, d, e, f\"\n * - If d() throws an expected error, then its shown call stack will be \"e, f\"\n * - If d() throws an unexpected error, then its shown call stack will be \"d, e, f\"\n * - If e() throws an error, then its shown call stack will be \"e, f\"\n *\n * Additionally, an error can inject additional \"virtual\" stack frames using the\n * injectVirtualStackFrame(error, filename) function: those are injected as a\n * replacement of the hidden frames.\n * In the example above, if we called injectVirtualStackFrame(err, \"h\") and\n * injectVirtualStackFrame(err, \"i\") on the expected error thrown by c(), its\n * shown call stack would have been \"h, i, e, f\".\n * This can be useful, for example, to report config validation errors as if they\n * were directly thrown in the config file.\n */\n\nconst ErrorToString = Function.call.bind(Error.prototype.toString);\n\nconst SUPPORTED =\n  !!Error.captureStackTrace &&\n  Object.getOwnPropertyDescriptor(Error, \"stackTraceLimit\")?.writable === true;\n\nconst START_HIDING = \"startHiding - secret - don't use this - v1\";\nconst STOP_HIDING = \"stopHiding - secret - don't use this - v1\";\n\ntype CallSite = NodeJS.CallSite;\n\nconst expectedErrors = new WeakSet<Error>();\nconst virtualFrames = new WeakMap<Error, CallSite[]>();\n\nfunction CallSite(filename: string): CallSite {\n  // We need to use a prototype otherwise it breaks source-map-support's internals\n  return Object.create({\n    isNative: () => false,\n    isConstructor: () => false,\n    isToplevel: () => true,\n    getFileName: () => filename,\n    getLineNumber: () => undefined,\n    getColumnNumber: () => undefined,\n    getFunctionName: () => undefined,\n    getMethodName: () => undefined,\n    getTypeName: () => undefined,\n    toString: () => filename,\n  } as CallSite);\n}\n\nexport function injectVirtualStackFrame(error: Error, filename: string) {\n  if (!SUPPORTED) return;\n\n  let frames = virtualFrames.get(error);\n  if (!frames) virtualFrames.set(error, (frames = []));\n  frames.push(CallSite(filename));\n\n  return error;\n}\n\nexport function expectedError(error: Error) {\n  if (!SUPPORTED) return;\n  expectedErrors.add(error);\n  return error;\n}\n\nexport function beginHiddenCallStack<A extends unknown[], R>(\n  fn: (...args: A) => R,\n) {\n  if (!SUPPORTED) return fn;\n\n  return Object.defineProperty(\n    function (...args: A) {\n      setupPrepareStackTrace();\n      return fn(...args);\n    },\n    \"name\",\n    { value: STOP_HIDING },\n  );\n}\n\nexport function endHiddenCallStack<A extends unknown[], R>(\n  fn: (...args: A) => R,\n) {\n  if (!SUPPORTED) return fn;\n\n  return Object.defineProperty(\n    function (...args: A) {\n      return fn(...args);\n    },\n    \"name\",\n    { value: START_HIDING },\n  );\n}\n\nfunction setupPrepareStackTrace() {\n  // @ts-expect-error This function is a singleton\n  setupPrepareStackTrace = () => {};\n\n  const { prepareStackTrace = defaultPrepareStackTrace } = Error;\n\n  // We add some extra frames to Error.stackTraceLimit, so that we can\n  // always show some useful frames even after deleting ours.\n  // STACK_TRACE_LIMIT_DELTA should be around the maximum expected number\n  // of internal frames, and not too big because capturing the stack trace\n  // is slow (this is why Error.stackTraceLimit does not default to Infinity!).\n  // Increase it if needed.\n  // However, we only do it if the user did not explicitly set it to 0.\n  const MIN_STACK_TRACE_LIMIT = 50;\n  Error.stackTraceLimit &&= Math.max(\n    Error.stackTraceLimit,\n    MIN_STACK_TRACE_LIMIT,\n  );\n\n  Error.prepareStackTrace = function stackTraceRewriter(err, trace) {\n    let newTrace = [];\n\n    const isExpected = expectedErrors.has(err);\n    let status: \"showing\" | \"hiding\" | \"unknown\" = isExpected\n      ? \"hiding\"\n      : \"unknown\";\n    for (let i = 0; i < trace.length; i++) {\n      const name = trace[i].getFunctionName();\n      if (name === START_HIDING) {\n        status = \"hiding\";\n      } else if (name === STOP_HIDING) {\n        if (status === \"hiding\") {\n          status = \"showing\";\n          if (virtualFrames.has(err)) {\n            newTrace.unshift(...virtualFrames.get(err));\n          }\n        } else if (status === \"unknown\") {\n          // Unexpected internal error, show the full stack trace\n          newTrace = trace;\n          break;\n        }\n      } else if (status !== \"hiding\") {\n        newTrace.push(trace[i]);\n      }\n    }\n\n    return prepareStackTrace(err, newTrace);\n  };\n}\n\nfunction defaultPrepareStackTrace(err: Error, trace: CallSite[]) {\n  if (trace.length === 0) return ErrorToString(err);\n  return `${ErrorToString(err)}\\n    at ${trace.join(\"\\n    at \")}`;\n}\n"], "mappings": ";;;;;;;;;;AA4CA,MAAMA,aAAa,GAAGC,QAAQ,CAACC,IAAI,CAACC,IAAI,CAACC,KAAK,CAACC,SAAS,CAACC,QAAQ,CAAC;AAElE,MAAMC,SAAS,GACb,CAAC,CAACH,KAAK,CAACI,iBAAiB,IACzB,EAAAC,qBAAA,GAAAC,MAAM,CAACC,wBAAwB,CAACP,KAAK,EAAE,iBAAiB,CAAC,qBAAzDK,qBAAA,CAA2DG,QAAQ,MAAK,IAAI;AAE9E,MAAMC,YAAY,GAAG,4CAA4C;AACjE,MAAMC,WAAW,GAAG,2CAA2C;AAI/D,MAAMC,cAAc,GAAG,IAAIC,OAAO,CAAQ,CAAC;AAC3C,MAAMC,aAAa,GAAG,IAAIC,OAAO,CAAoB,CAAC;AAEtD,SAASC,QAAQA,CAACC,QAAgB,EAAY;EAE5C,OAAOV,MAAM,CAACW,MAAM,CAAC;IACnBC,QAAQ,EAAEA,CAAA,KAAM,KAAK;IACrBC,aAAa,EAAEA,CAAA,KAAM,KAAK;IAC1BC,UAAU,EAAEA,CAAA,KAAM,IAAI;IACtBC,WAAW,EAAEA,CAAA,KAAML,QAAQ;IAC3BM,aAAa,EAAEA,CAAA,KAAMC,SAAS;IAC9BC,eAAe,EAAEA,CAAA,KAAMD,SAAS;IAChCE,eAAe,EAAEA,CAAA,KAAMF,SAAS;IAChCG,aAAa,EAAEA,CAAA,KAAMH,SAAS;IAC9BI,WAAW,EAAEA,CAAA,KAAMJ,SAAS;IAC5BrB,QAAQ,EAAEA,CAAA,KAAMc;EAClB,CAAa,CAAC;AAChB;AAEO,SAASY,uBAAuBA,CAACC,KAAY,EAAEb,QAAgB,EAAE;EACtE,IAAI,CAACb,SAAS,EAAE;EAEhB,IAAI2B,MAAM,GAAGjB,aAAa,CAACkB,GAAG,CAACF,KAAK,CAAC;EACrC,IAAI,CAACC,MAAM,EAAEjB,aAAa,CAACmB,GAAG,CAACH,KAAK,EAAGC,MAAM,GAAG,EAAG,CAAC;EACpDA,MAAM,CAACG,IAAI,CAAClB,QAAQ,CAACC,QAAQ,CAAC,CAAC;EAE/B,OAAOa,KAAK;AACd;AAEO,SAASK,aAAaA,CAACL,KAAY,EAAE;EAC1C,IAAI,CAAC1B,SAAS,EAAE;EAChBQ,cAAc,CAACwB,GAAG,CAACN,KAAK,CAAC;EACzB,OAAOA,KAAK;AACd;AAEO,SAASO,oBAAoBA,CAClCC,EAAqB,EACrB;EACA,IAAI,CAAClC,SAAS,EAAE,OAAOkC,EAAE;EAEzB,OAAO/B,MAAM,CAACgC,cAAc,CAC1B,UAAU,GAAGC,IAAO,EAAE;IACpBC,sBAAsB,CAAC,CAAC;IACxB,OAAOH,EAAE,CAAC,GAAGE,IAAI,CAAC;EACpB,CAAC,EACD,MAAM,EACN;IAAEE,KAAK,EAAE/B;EAAY,CACvB,CAAC;AACH;AAEO,SAASgC,kBAAkBA,CAChCL,EAAqB,EACrB;EACA,IAAI,CAAClC,SAAS,EAAE,OAAOkC,EAAE;EAEzB,OAAO/B,MAAM,CAACgC,cAAc,CAC1B,UAAU,GAAGC,IAAO,EAAE;IACpB,OAAOF,EAAE,CAAC,GAAGE,IAAI,CAAC;EACpB,CAAC,EACD,MAAM,EACN;IAAEE,KAAK,EAAEhC;EAAa,CACxB,CAAC;AACH;AAEA,SAAS+B,sBAAsBA,CAAA,EAAG;EAEhCA,sBAAsB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAEjC,MAAM;IAAEG,iBAAiB,GAAGC;EAAyB,CAAC,GAAG5C,KAAK;EAS9D,MAAM6C,qBAAqB,GAAG,EAAE;EAChC7C,KAAK,CAAC8C,eAAe,KAArB9C,KAAK,CAAC8C,eAAe,GAAKC,IAAI,CAACC,GAAG,CAChChD,KAAK,CAAC8C,eAAe,EACrBD,qBACF,CAAC;EAED7C,KAAK,CAAC2C,iBAAiB,GAAG,SAASM,kBAAkBA,CAACC,GAAG,EAAEC,KAAK,EAAE;IAChE,IAAIC,QAAQ,GAAG,EAAE;IAEjB,MAAMC,UAAU,GAAG1C,cAAc,CAAC2C,GAAG,CAACJ,GAAG,CAAC;IAC1C,IAAIK,MAAwC,GAAGF,UAAU,GACrD,QAAQ,GACR,SAAS;IACb,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACM,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,MAAME,IAAI,GAAGP,KAAK,CAACK,CAAC,CAAC,CAAC/B,eAAe,CAAC,CAAC;MACvC,IAAIiC,IAAI,KAAKjD,YAAY,EAAE;QACzB8C,MAAM,GAAG,QAAQ;MACnB,CAAC,MAAM,IAAIG,IAAI,KAAKhD,WAAW,EAAE;QAC/B,IAAI6C,MAAM,KAAK,QAAQ,EAAE;UACvBA,MAAM,GAAG,SAAS;UAClB,IAAI1C,aAAa,CAACyC,GAAG,CAACJ,GAAG,CAAC,EAAE;YAC1BE,QAAQ,CAACO,OAAO,CAAC,GAAG9C,aAAa,CAACkB,GAAG,CAACmB,GAAG,CAAC,CAAC;UAC7C;QACF,CAAC,MAAM,IAAIK,MAAM,KAAK,SAAS,EAAE;UAE/BH,QAAQ,GAAGD,KAAK;UAChB;QACF;MACF,CAAC,MAAM,IAAII,MAAM,KAAK,QAAQ,EAAE;QAC9BH,QAAQ,CAACnB,IAAI,CAACkB,KAAK,CAACK,CAAC,CAAC,CAAC;MACzB;IACF;IAEA,OAAOb,iBAAiB,CAACO,GAAG,EAAEE,QAAQ,CAAC;EACzC,CAAC;AACH;AAEA,SAASR,wBAAwBA,CAACM,GAAU,EAAEC,KAAiB,EAAE;EAC/D,IAAIA,KAAK,CAACM,MAAM,KAAK,CAAC,EAAE,OAAO7D,aAAa,CAACsD,GAAG,CAAC;EACjD,OAAO,GAAGtD,aAAa,CAACsD,GAAG,CAAC,YAAYC,KAAK,CAACS,IAAI,CAAC,WAAW,CAAC,EAAE;AACnE;AAAC", "ignoreList": []}
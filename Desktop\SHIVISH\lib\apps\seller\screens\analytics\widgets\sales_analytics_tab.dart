import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';

class SalesAnalyticsTab extends StatelessWidget {
  const SalesAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'Something went wrong',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        final salesAnalytics = state.salesAnalytics;
        if (salesAnalytics == null) {
          return const Center(child: Text('No sales data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCards(context, salesAnalytics),
              const SizedBox(height: 24),
              _buildSalesChart(context, salesAnalytics),
              const SizedBox(height: 24),
              _buildCategoryBreakdown(context, salesAnalytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(BuildContext context, SalesAnalytics analytics) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          context,
          'Total Revenue',
          CurrencyFormatter.format(analytics.totalRevenue),
          Icons.attach_money,
          Colors.green,
        ),
        _buildMetricCard(
          context,
          'Total Orders',
          analytics.totalOrders.toString(),
          Icons.shopping_cart,
          Colors.blue,
        ),
        _buildMetricCard(
          context,
          'Average Order Value',
          CurrencyFormatter.format(analytics.averageOrderValue),
          Icons.trending_up,
          Colors.orange,
        ),
        _buildMetricCard(
          context,
          'Growth Rate',
          '${analytics.growthRate.toStringAsFixed(1)}%',
          Icons.show_chart,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildMetricCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesChart(BuildContext context, SalesAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Sales',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: analytics.dailySales
                          .map((point) => FlSpot(
                                point.date.millisecondsSinceEpoch.toDouble(),
                                point.amount,
                              ))
                          .toList(),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryBreakdown(
      BuildContext context, SalesAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sales by Category',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.salesByCategory.entries.map((entry) {
              final percentage = (entry.value / analytics.totalRevenue * 100)
                  .toStringAsFixed(1);
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(entry.key),
                        Text('$percentage%'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: entry.value / analytics.totalRevenue,
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.blue.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

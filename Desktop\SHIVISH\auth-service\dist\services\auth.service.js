"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const jwt_service_1 = require("./jwt.service");
const password_service_1 = require("./password.service");
const database_service_1 = require("./database.service");
const logger_1 = require("../utils/logger");
const user_model_1 = require("../models/user.model");
class AuthService {
    async register(request) {
        try {
            const existingUser = await database_service_1.databaseService.query('SELECT id FROM users WHERE email = $1', [request.email]);
            if (existingUser.rows.length > 0) {
                throw new Error('User already exists with this email');
            }
            const passwordValidation = password_service_1.passwordService.validatePassword(request.password);
            if (!passwordValidation.isValid) {
                throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
            }
            const passwordHash = await password_service_1.passwordService.hashPassword(request.password);
            const userResult = await database_service_1.databaseService.query(`INSERT INTO users (email, password_hash, display_name, phone_number, role, status, email_verified, phone_verified)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING *`, [
                request.email,
                passwordHash,
                request.displayName || null,
                request.phoneNumber || null,
                request.role,
                'active',
                false,
                false
            ]);
            const user = user_model_1.UserMapper.fromRow(userResult.rows[0]);
            const tokens = jwt_service_1.jwtService.generateTokenPair(user.id, user.email, user.role);
            await database_service_1.databaseService.query(`INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
         VALUES ($1, $2, $3, $4, $5)`, [
                user.id,
                tokens.refreshToken,
                request.deviceInfo ? JSON.stringify(request.deviceInfo) : null,
                request.ipAddress,
                tokens.refreshTokenExpiry
            ]);
            return {
                user: user_model_1.UserMapper.toProfile(user),
                tokens: {
                    accessToken: tokens.accessToken,
                    refreshToken: tokens.refreshToken,
                    expiresAt: tokens.accessTokenExpiry,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Registration failed:', error);
            throw error;
        }
    }
    async login(request) {
        try {
            const userResult = await database_service_1.databaseService.query('SELECT * FROM users WHERE email = $1', [request.email]);
            if (userResult.rows.length === 0) {
                throw new Error('Invalid email or password');
            }
            const userRow = userResult.rows[0];
            const user = user_model_1.UserMapper.fromRow(userRow);
            if (user.lockedUntil && user.lockedUntil > new Date()) {
                throw new Error('Account is temporarily locked due to too many failed login attempts');
            }
            const isPasswordValid = await password_service_1.passwordService.verifyPassword(request.password, user.passwordHash);
            if (!isPasswordValid) {
                await database_service_1.databaseService.query(`UPDATE users 
           SET failed_login_attempts = failed_login_attempts + 1,
               locked_until = CASE 
                 WHEN failed_login_attempts + 1 >= 5 THEN NOW() + INTERVAL '15 minutes'
                 ELSE NULL
               END
           WHERE id = $1`, [user.id]);
                throw new Error('Invalid email or password');
            }
            await database_service_1.databaseService.query(`UPDATE users 
         SET failed_login_attempts = 0, locked_until = NULL, last_login_at = NOW()
         WHERE id = $1`, [user.id]);
            const tokens = jwt_service_1.jwtService.generateTokenPair(user.id, user.email, user.role);
            await database_service_1.databaseService.query(`INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
         VALUES ($1, $2, $3, $4, $5)`, [
                user.id,
                tokens.refreshToken,
                request.deviceInfo ? JSON.stringify(request.deviceInfo) : null,
                request.ipAddress,
                tokens.refreshTokenExpiry
            ]);
            return {
                user: user_model_1.UserMapper.toProfile(user),
                tokens: {
                    accessToken: tokens.accessToken,
                    refreshToken: tokens.refreshToken,
                    expiresAt: tokens.accessTokenExpiry,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Login failed:', error);
            throw error;
        }
    }
    async refreshToken(refreshToken) {
        try {
            const payload = jwt_service_1.jwtService.verifyRefreshToken(refreshToken);
            const sessionResult = await database_service_1.databaseService.query('SELECT * FROM user_sessions WHERE refresh_token = $1 AND expires_at > NOW()', [refreshToken]);
            if (sessionResult.rows.length === 0) {
                throw new Error('Invalid or expired refresh token');
            }
            const userResult = await database_service_1.databaseService.query('SELECT * FROM users WHERE id = $1', [payload.sub]);
            if (userResult.rows.length === 0) {
                throw new Error('User not found');
            }
            const user = user_model_1.UserMapper.fromRow(userResult.rows[0]);
            const accessToken = jwt_service_1.jwtService.refreshAccessToken(refreshToken, user.email, user.role);
            await database_service_1.databaseService.query('UPDATE user_sessions SET last_used_at = NOW() WHERE refresh_token = $1', [refreshToken]);
            return {
                accessToken,
                expiresAt: new Date(Date.now() + 15 * 60 * 1000),
            };
        }
        catch (error) {
            logger_1.logger.error('Token refresh failed:', error);
            throw error;
        }
    }
    async logout(refreshToken) {
        try {
            await database_service_1.databaseService.query('DELETE FROM user_sessions WHERE refresh_token = $1', [refreshToken]);
        }
        catch (error) {
            logger_1.logger.error('Logout failed:', error);
            throw error;
        }
    }
    async forgotPassword(email) {
        try {
            const userResult = await database_service_1.databaseService.query('SELECT * FROM users WHERE email = $1', [email]);
            if (userResult.rows.length === 0) {
                return;
            }
            const user = user_model_1.UserMapper.fromRow(userResult.rows[0]);
            const resetToken = password_service_1.passwordService.generateResetToken();
            const expiresAt = new Date(Date.now() + 60 * 60 * 1000);
            await database_service_1.databaseService.query(`INSERT INTO password_reset_tokens (user_id, token, expires_at)
         VALUES ($1, $2, $3)`, [user.id, resetToken, expiresAt]);
            logger_1.logger.info(`Password reset token for ${email}: ${resetToken}`);
        }
        catch (error) {
            logger_1.logger.error('Forgot password failed:', error);
            throw error;
        }
    }
    async resetPassword(token, newPassword) {
        try {
            const tokenResult = await database_service_1.databaseService.query(`SELECT * FROM password_reset_tokens 
         WHERE token = $1 AND expires_at > NOW() AND used = false`, [token]);
            if (tokenResult.rows.length === 0) {
                throw new Error('Invalid or expired reset token');
            }
            const resetToken = tokenResult.rows[0];
            const passwordValidation = password_service_1.passwordService.validatePassword(newPassword);
            if (!passwordValidation.isValid) {
                throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
            }
            const passwordHash = await password_service_1.passwordService.hashPassword(newPassword);
            await database_service_1.databaseService.transaction(async (client) => {
                await client.query('UPDATE users SET password_hash = $1 WHERE id = $2', [passwordHash, resetToken.user_id]);
                await client.query('UPDATE password_reset_tokens SET used = true WHERE id = $1', [resetToken.id]);
            });
        }
        catch (error) {
            logger_1.logger.error('Reset password failed:', error);
            throw error;
        }
    }
    async verifyEmail(token) {
        try {
            logger_1.logger.info(`Email verification token: ${token}`);
        }
        catch (error) {
            logger_1.logger.error('Email verification failed:', error);
            throw error;
        }
    }
    async resendVerification(email) {
        try {
            logger_1.logger.info(`Resend verification for: ${email}`);
        }
        catch (error) {
            logger_1.logger.error('Resend verification failed:', error);
            throw error;
        }
    }
}
exports.AuthService = AuthService;
//# sourceMappingURL=auth.service.js.map
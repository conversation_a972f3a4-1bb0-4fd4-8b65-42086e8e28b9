import 'package:flutter/material.dart';
import 'package:shivish/apps/seller/domain/models/payment_model.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';

class TransactionListItem extends StatelessWidget {
  final TransactionModel transaction;

  const TransactionListItem({
    super.key,
    required this.transaction,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        leading: CircleAvatar(
          backgroundColor: transaction.isCredit
              ? theme.colorScheme.primary.withOpacity(0.1)
              : theme.colorScheme.error.withOpacity(0.1),
          child: Icon(
            transaction.isCredit ? Icons.add : Icons.remove,
            color: transaction.isCredit
                ? theme.colorScheme.primary
                : theme.colorScheme.error,
          ),
        ),
        title: Text(
          transaction.description,
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Text(
          DateFormatter.format(transaction.date),
          style: theme.textTheme.bodySmall,
        ),
        trailing: Text(
          '${transaction.isCredit ? '+' : '-'}${CurrencyFormatter.format(transaction.amount)}',
          style: theme.textTheme.titleMedium?.copyWith(
            color: transaction.isCredit
                ? theme.colorScheme.primary
                : theme.colorScheme.error,
          ),
        ),
      ),
    );
  }
}

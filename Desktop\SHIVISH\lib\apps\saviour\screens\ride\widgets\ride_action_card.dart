import 'package:flutter/material.dart';
import '../../../../../../shared/models/ride/ride_request_model.dart';
import '../../../../../../shared/utils/distance_calculator.dart';

class RideActionCard extends StatelessWidget {
  final RideRequestModel ride;
  final VoidCallback? onNavigatePressed;
  final VoidCallback? onCallPressed;
  final VoidCallback? onArrivedPressed;
  final VoidCallback? onStartRidePressed;
  final VoidCallback? onCompletePressed;
  final VoidCallback? onCancelPressed;

  const RideActionCard({
    super.key,
    required this.ride,
    this.onNavigatePressed,
    this.onCallPressed,
    this.onArrivedPressed,
    this.onStartRidePressed,
    this.onCompletePressed,
    this.onCancelPressed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Status header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _getStatusColor(ride.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _getStatusText(ride.status),
                  style: TextStyle(
                    color: _getStatusColor(ride.status),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Spacer(),
              // Navigation button
              if (onNavigatePressed != null)
                IconButton(
                  onPressed: onNavigatePressed,
                  icon: const Icon(Icons.directions),
                  tooltip: 'Navigate',
                  color: theme.colorScheme.primary,
                ),
              // Call button
              if (onCallPressed != null)
                IconButton(
                  onPressed: onCallPressed,
                  icon: const Icon(Icons.phone),
                  tooltip: 'Call Passenger',
                  color: theme.colorScheme.primary,
                ),
            ],
          ),
          const SizedBox(height: 16),

          // Passenger info
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                child: Text(
                  ride.userName != null && ride.userName!.isNotEmpty
                      ? ride.userName![0].toUpperCase()
                      : 'U',
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      ride.userName ?? 'Passenger',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (ride.userPhone != null && ride.userPhone!.isNotEmpty)
                      Text(
                        ride.userPhone!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
              ),
              // Payment method
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getPaymentIcon(ride.paymentMethod),
                      size: 16,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _getPaymentText(ride.paymentMethod),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Ride details
          Row(
            children: [
              // Distance
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Distance',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      DistanceCalculator.formatDistance(ride.distance),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Duration
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Duration',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      DistanceCalculator.formatTime(ride.estimatedDuration),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),

              // Fare
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Fare',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      '₹${ride.estimatedFare.toStringAsFixed(0)}',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              // Primary action button
              Expanded(
                child: _buildPrimaryActionButton(context, theme),
              ),
              const SizedBox(width: 8),
              // Cancel button
              if (onCancelPressed != null)
                TextButton.icon(
                  onPressed: onCancelPressed,
                  icon: const Icon(Icons.cancel),
                  label: const Text('Cancel'),
                  style: TextButton.styleFrom(
                    foregroundColor: theme.colorScheme.error,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrimaryActionButton(BuildContext context, ThemeData theme) {
    switch (ride.status) {
      case RideStatus.accepted:
        return ElevatedButton.icon(
          onPressed: onArrivedPressed,
          icon: const Icon(Icons.location_on),
          label: const Text('I\'ve Arrived'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        );
      case RideStatus.arrived:
        return ElevatedButton.icon(
          onPressed: onStartRidePressed,
          icon: const Icon(Icons.play_arrow),
          label: const Text('Start Ride'),
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        );
      case RideStatus.inProgress:
        return ElevatedButton.icon(
          onPressed: onCompletePressed,
          icon: const Icon(Icons.check_circle),
          label: const Text('Complete Ride'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        );
      case RideStatus.completed:
        return ElevatedButton.icon(
          onPressed: null,
          icon: const Icon(Icons.check_circle),
          label: const Text('Completed'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green.withOpacity(0.5),
            foregroundColor: theme.colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Color _getStatusColor(RideStatus status) {
    switch (status) {
      case RideStatus.pending:
        return Colors.orange;
      case RideStatus.accepted:
        return Colors.blue;
      case RideStatus.arrived:
        return Colors.purple;
      case RideStatus.inProgress:
        return Colors.indigo;
      case RideStatus.completed:
        return Colors.green;
      case RideStatus.cancelled:
        return Colors.red;
      case RideStatus.expired:
        return Colors.grey;
    }
  }

  String _getStatusText(RideStatus status) {
    switch (status) {
      case RideStatus.pending:
        return 'Pending';
      case RideStatus.accepted:
        return 'Accepted';
      case RideStatus.arrived:
        return 'Arrived';
      case RideStatus.inProgress:
        return 'In Progress';
      case RideStatus.completed:
        return 'Completed';
      case RideStatus.cancelled:
        return 'Cancelled';
      case RideStatus.expired:
        return 'Expired';
    }
  }

  IconData _getPaymentIcon(RidePaymentMethod method) {
    switch (method) {
      case RidePaymentMethod.cash:
        return Icons.money;
      case RidePaymentMethod.card:
        return Icons.credit_card;
      case RidePaymentMethod.wallet:
        return Icons.account_balance_wallet;
      case RidePaymentMethod.upi:
        return Icons.account_balance;
    }
  }

  String _getPaymentText(RidePaymentMethod method) {
    switch (method) {
      case RidePaymentMethod.cash:
        return 'Cash';
      case RidePaymentMethod.card:
        return 'Card';
      case RidePaymentMethod.wallet:
        return 'Wallet';
      case RidePaymentMethod.upi:
        return 'UPI';
    }
  }
}

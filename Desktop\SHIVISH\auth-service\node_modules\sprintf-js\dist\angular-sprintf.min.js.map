{"version": 3, "file": "angular-sprintf.min.js", "sources": ["../src/angular-sprintf.js"], "names": ["angular", "module", "filter", "sprintf", "apply", "arguments", "$filter", "format", "argv", "vsprintf"], "mappings": ";;AAAAA,QACIC,OAAO,cACPC,OAAO,UAAW,WACd,MAAO,YACH,MAAOC,SAAQC,MAAM,KAAMC,cAGnCH,OAAO,OAAQ,UAAW,SAASI,GAC/B,MAAOA,GAAQ,cAEnBJ,OAAO,WAAY,WACf,MAAO,UAASK,EAAQC,GACpB,MAAOC,UAASF,EAAQC,MAGhCN,OAAO,QAAS,UAAW,SAASI,GAChC,MAAOA,GAAQ"}
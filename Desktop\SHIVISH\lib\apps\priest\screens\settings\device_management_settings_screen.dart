import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/priest/presentation/cubits/priest_cubit.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/messages/success_message.dart';
import 'package:shivish/shared/ui_components/dialogs/app_dialog.dart';

class DeviceManagementSettingsScreen extends StatefulWidget {
  const DeviceManagementSettingsScreen({super.key});

  @override
  State<DeviceManagementSettingsScreen> createState() =>
      _DeviceManagementSettingsScreenState();
}

class _DeviceManagementSettingsScreenState
    extends State<DeviceManagementSettingsScreen> {
  bool _requireDeviceVerification = true;
  bool _showLoginHistory = true;

  @override
  void initState() {
    super.initState();
    _loadDeviceSettings();
  }

  void _loadDeviceSettings() {
    final state = context.read<PriestCubit>().state;
    state.maybeWhen(
      loaded: (priest) {
        if (priest.deviceSettings != null) {
          setState(() {
            _requireDeviceVerification =
                priest.deviceSettings!['requireVerification'] ?? true;
            _showLoginHistory = priest.deviceSettings!['showHistory'] ?? true;
          });
        }
      },
      orElse: () {},
    );
  }

  Future<void> _updateDeviceSettings(String key, bool value) async {
    final deviceSettings = {
      'requireVerification':
          key == 'verification' ? value : _requireDeviceVerification,
      'showHistory': key == 'history' ? value : _showLoginHistory,
    };

    context.read<PriestCubit>().updateDeviceSettings(deviceSettings);
    setState(() {
      if (key == 'verification') {
        _requireDeviceVerification = value;
      } else if (key == 'history') {
        _showLoginHistory = value;
      }
    });
  }

  Future<void> _removeDevice(Map<String, dynamic> device) async {
    final result = await AppDialogs.showConfirm(
      context: context,
      title: 'Remove Device',
      message:
          'Are you sure you want to remove ${device['name']} from your account?',
      confirmText: 'Remove',
      cancelText: 'Cancel',
    );

    if (result == true && mounted) {
      context.read<PriestCubit>().removeDevice(device['id']);
      ScaffoldMessenger.of(context).showSnackBar(
        SuccessMessage(message: '${device['name']} removed successfully'),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<PriestCubit, PriestState>(
      builder: (context, state) {
        return Scaffold(
          appBar: const AppToolbar(
            title: 'Device Management',
          ),
          body: state.maybeWhen(
            loading: () => const LoadingIndicator(),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            loaded: (priest) => _buildContent(context, priest.id),
            orElse: () => const Center(child: Text('Unknown State')),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, String priestId) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Connected Devices',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Manage devices that have access to your account.',
                    style: TextStyle(color: Colors.grey),
                  ),
                  const SizedBox(height: 16),
                  BlocBuilder<PriestCubit, PriestState>(
                    builder: (context, state) {
                      return state.maybeWhen(
                        loaded: (priest) {
                          final devices = priest.devices;
                          return ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: devices.length,
                            itemBuilder: (context, index) {
                              final device = devices[index];
                              return Column(
                                children: [
                                  ListTile(
                                    leading: Icon(
                                      device['type'] == 'Mobile'
                                          ? Icons.phone_android
                                          : Icons.computer,
                                      color: Theme.of(context).primaryColor,
                                    ),
                                    title: Text(device['name']),
                                    subtitle: Text(
                                      'Last active: ${device['lastActive']}',
                                      style:
                                          const TextStyle(color: Colors.grey),
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        if (device['isCurrent'])
                                          Container(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                              vertical: 4,
                                            ),
                                            decoration: BoxDecoration(
                                              color:
                                                  Colors.green.withOpacity(0.1),
                                              borderRadius:
                                                  BorderRadius.circular(12),
                                            ),
                                            child: const Text(
                                              'Current',
                                              style: TextStyle(
                                                color: Colors.green,
                                                fontSize: 12,
                                              ),
                                            ),
                                          ),
                                        IconButton(
                                          icon:
                                              const Icon(Icons.delete_outline),
                                          onPressed: () =>
                                              _removeDevice(device),
                                        ),
                                      ],
                                    ),
                                  ),
                                  if (index < devices.length - 1)
                                    const Divider(),
                                ],
                              );
                            },
                          );
                        },
                        orElse: () =>
                            const Center(child: Text('No devices found')),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Security Settings',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Require Device Verification'),
                    subtitle: const Text(
                      'Require verification when logging in from a new device',
                    ),
                    value: _requireDeviceVerification,
                    onChanged: (value) =>
                        _updateDeviceSettings('verification', value),
                  ),
                  const Divider(),
                  SwitchListTile(
                    title: const Text('Show Login History'),
                    subtitle: const Text(
                      'Display detailed login history for all devices',
                    ),
                    value: _showLoginHistory,
                    onChanged: (value) =>
                        _updateDeviceSettings('history', value),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:fl_chart/fl_chart.dart';

class PerformanceMetricsTab extends StatelessWidget {
  const PerformanceMetricsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'Something went wrong',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        final performanceMetrics = state.performanceMetrics;
        if (performanceMetrics == null) {
          return const Center(child: Text('No performance data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCards(context, performanceMetrics),
              const SizedBox(height: 24),
              _buildPerformanceChart(context, performanceMetrics),
              const SizedBox(height: 24),
              _buildImprovements(context, performanceMetrics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(BuildContext context, PerformanceMetrics metrics) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          context,
          'Conversion Rate',
          '${metrics.conversionRate.toStringAsFixed(1)}%',
          Icons.trending_up,
          Colors.green,
        ),
        _buildMetricCard(
          context,
          'Return Rate',
          '${metrics.returnRate.toStringAsFixed(1)}%',
          Icons.assignment_return,
          Colors.orange,
        ),
        _buildMetricCard(
          context,
          'Processing Time',
          '${metrics.averageProcessingTime.toStringAsFixed(1)} hrs',
          Icons.timer,
          Colors.blue,
        ),
        _buildMetricCard(
          context,
          'Delivery Time',
          '${metrics.averageDeliveryTime.toStringAsFixed(1)} hrs',
          Icons.local_shipping,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildMetricCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceChart(
      BuildContext context, PerformanceMetrics metrics) {
    final entries = metrics.performanceByDay.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Daily Performance',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: false),
                  titlesData: const FlTitlesData(show: false),
                  borderData: FlBorderData(show: false),
                  lineBarsData: [
                    LineChartBarData(
                      spots: entries
                          .asMap()
                          .entries
                          .map((e) =>
                              FlSpot(e.key.toDouble(), e.value.value.value))
                          .toList(),
                      isCurved: true,
                      color: Colors.blue,
                      barWidth: 2,
                      dotData: const FlDotData(show: false),
                      belowBarData: BarAreaData(
                        show: true,
                        color: Colors.blue.withOpacity(0.1),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImprovements(BuildContext context, PerformanceMetrics metrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Suggested Improvements',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...metrics.improvements.map((improvement) => ListTile(
                  leading: const Icon(Icons.lightbulb_outline),
                  title: Text(improvement),
                )),
          ],
        ),
      ),
    );
  }
}

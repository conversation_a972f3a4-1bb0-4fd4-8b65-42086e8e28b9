import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/utils/logger.dart';
import '../../services/saviour_notification_service.dart';

final _logger = getLogger('NotificationSettingsScreen');

/// Provider for notification settings
final notificationSettingsProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final notificationService = ref.watch(saviourNotificationServiceProvider);
  return notificationService.getNotificationPreferences();
});

class NotificationSettingsScreen extends ConsumerStatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  ConsumerState<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends ConsumerState<NotificationSettingsScreen> {
  bool _enableDeliveryNotifications = true;
  bool _enableEarningsNotifications = true;
  bool _enableSoundAndVibration = true;
  bool _isLoading = false;
  
  @override
  void initState() {
    super.initState();
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      final settings = await ref.read(saviourNotificationServiceProvider).getNotificationPreferences();
      
      setState(() {
        _enableDeliveryNotifications = settings['enableDeliveryNotifications'] ?? true;
        _enableEarningsNotifications = settings['enableEarningsNotifications'] ?? true;
        _enableSoundAndVibration = settings['enableSoundAndVibration'] ?? true;
      });
    } catch (e) {
      _logger.severe('Error loading notification settings: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });
    
    try {
      await ref.read(saviourNotificationServiceProvider).saveNotificationPreferences(
        enableDeliveryNotifications: _enableDeliveryNotifications,
        enableEarningsNotifications: _enableEarningsNotifications,
        enableSoundAndVibration: _enableSoundAndVibration,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Notification settings saved'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error saving notification settings: $e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Settings'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                // Notification types section
                _buildSectionHeader('Notification Types'),
                
                // Delivery notifications
                SwitchListTile(
                  title: const Text('Delivery Notifications'),
                  subtitle: const Text('Receive notifications about new delivery requests and updates'),
                  value: _enableDeliveryNotifications,
                  onChanged: (value) {
                    setState(() {
                      _enableDeliveryNotifications = value;
                    });
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.local_shipping,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
                
                const Divider(),
                
                // Earnings notifications
                SwitchListTile(
                  title: const Text('Earnings Notifications'),
                  subtitle: const Text('Receive notifications about earnings and payouts'),
                  value: _enableEarningsNotifications,
                  onChanged: (value) {
                    setState(() {
                      _enableEarningsNotifications = value;
                    });
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.attach_money,
                      color: Colors.orange,
                    ),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Notification behavior section
                _buildSectionHeader('Notification Behavior'),
                
                // Sound and vibration
                SwitchListTile(
                  title: const Text('Sound and Vibration'),
                  subtitle: const Text('Enable sound and vibration for notifications'),
                  value: _enableSoundAndVibration,
                  onChanged: (value) {
                    setState(() {
                      _enableSoundAndVibration = value;
                    });
                  },
                  secondary: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.purple.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.volume_up,
                      color: Colors.purple,
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // Save button
                ElevatedButton(
                  onPressed: _saveSettings,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                  child: const Text('Save Settings'),
                ),
                
                const SizedBox(height: 16),
                
                // Note
                const Card(
                  child: Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'Important Note',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          'Even if you disable delivery notifications, you will still receive important updates about your active deliveries.',
                          style: TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }
  
  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}

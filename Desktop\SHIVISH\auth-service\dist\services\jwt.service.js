"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.jwtService = exports.JWTService = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const uuid_1 = require("uuid");
const config_1 = require("../config/config");
const logger_1 = require("../utils/logger");
class JWTService {
    constructor() {
        this.accessTokenSecret = config_1.config.jwt.accessTokenSecret;
        this.refreshTokenSecret = config_1.config.jwt.refreshTokenSecret;
        this.issuer = config_1.config.jwt.issuer;
        this.audience = config_1.config.jwt.audience;
    }
    generateTokenPair(userId, email, role) {
        const sessionId = (0, uuid_1.v4)();
        const now = Math.floor(Date.now() / 1000);
        const accessPayload = {
            sub: userId,
            email,
            role,
            iat: now,
            exp: now + this.parseExpiry(config_1.config.jwt.accessTokenExpiry),
            iss: this.issuer,
            aud: this.audience,
        };
        const refreshPayload = {
            sub: userId,
            jti: sessionId,
            iat: now,
            exp: now + this.parseExpiry(config_1.config.jwt.refreshTokenExpiry),
            iss: this.issuer,
            aud: this.audience,
        };
        const accessToken = jsonwebtoken_1.default.sign(accessPayload, this.accessTokenSecret, {
            algorithm: 'HS256',
        });
        const refreshToken = jsonwebtoken_1.default.sign(refreshPayload, this.refreshTokenSecret, {
            algorithm: 'HS256',
        });
        return {
            accessToken,
            refreshToken,
            accessTokenExpiry: new Date(accessPayload.exp * 1000),
            refreshTokenExpiry: new Date(refreshPayload.exp * 1000),
        };
    }
    verifyAccessToken(token) {
        try {
            const payload = jsonwebtoken_1.default.verify(token, this.accessTokenSecret, {
                issuer: this.issuer,
                audience: this.audience,
                algorithms: ['HS256'],
            });
            return payload;
        }
        catch (error) {
            logger_1.logger.warn('Access token verification failed:', error);
            throw new Error('Invalid access token');
        }
    }
    verifyRefreshToken(token) {
        try {
            const payload = jsonwebtoken_1.default.verify(token, this.refreshTokenSecret, {
                issuer: this.issuer,
                audience: this.audience,
                algorithms: ['HS256'],
            });
            return payload;
        }
        catch (error) {
            logger_1.logger.warn('Refresh token verification failed:', error);
            throw new Error('Invalid refresh token');
        }
    }
    refreshAccessToken(refreshToken, email, role) {
        const refreshPayload = this.verifyRefreshToken(refreshToken);
        const now = Math.floor(Date.now() / 1000);
        const accessPayload = {
            sub: refreshPayload.sub,
            email,
            role,
            iat: now,
            exp: now + this.parseExpiry(config_1.config.jwt.accessTokenExpiry),
            iss: this.issuer,
            aud: this.audience,
        };
        return jsonwebtoken_1.default.sign(accessPayload, this.accessTokenSecret, {
            algorithm: 'HS256',
        });
    }
    extractTokenFromHeader(authHeader) {
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return null;
        }
        return authHeader.substring(7);
    }
    getTokenExpiry(token) {
        try {
            const decoded = jsonwebtoken_1.default.decode(token);
            if (decoded && decoded.exp) {
                return new Date(decoded.exp * 1000);
            }
            return null;
        }
        catch (error) {
            return null;
        }
    }
    isTokenExpired(token) {
        const expiry = this.getTokenExpiry(token);
        if (!expiry)
            return true;
        return expiry.getTime() < Date.now();
    }
    parseExpiry(expiry) {
        const unit = expiry.slice(-1);
        const value = parseInt(expiry.slice(0, -1), 10);
        switch (unit) {
            case 's': return value;
            case 'm': return value * 60;
            case 'h': return value * 60 * 60;
            case 'd': return value * 24 * 60 * 60;
            default: throw new Error(`Invalid expiry format: ${expiry}`);
        }
    }
}
exports.JWTService = JWTService;
exports.jwtService = new JWTService();
//# sourceMappingURL=jwt.service.js.map
import jwt from 'jsonwebtoken';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../config/config';
import { logger } from '../utils/logger';

export interface AccessTokenPayload {
  sub: string;        // user ID
  email: string;      // user email
  role: string;       // user role
  iat: number;        // issued at
  exp: number;        // expires at
  iss: string;        // issuer
  aud: string;        // audience
}

export interface RefreshTokenPayload {
  sub: string;        // user ID
  jti: string;        // token ID (session ID)
  iat: number;        // issued at
  exp: number;        // expires at
  iss: string;        // issuer
  aud: string;        // audience
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  accessTokenExpiry: Date;
  refreshTokenExpiry: Date;
}

export class JWTService {
  private readonly accessTokenSecret: string;
  private readonly refreshTokenSecret: string;
  private readonly issuer: string;
  private readonly audience: string;

  constructor() {
    this.accessTokenSecret = config.jwt.accessTokenSecret;
    this.refreshTokenSecret = config.jwt.refreshTokenSecret;
    this.issuer = config.jwt.issuer;
    this.audience = config.jwt.audience;
  }

  /**
   * Generate access and refresh token pair
   */
  generateTokenPair(userId: string, email: string, role: string): TokenPair {
    const sessionId = uuidv4();
    const now = Math.floor(Date.now() / 1000);

    // Access token payload
    const accessPayload: AccessTokenPayload = {
      sub: userId,
      email,
      role,
      iat: now,
      exp: now + this.parseExpiry(config.jwt.accessTokenExpiry),
      iss: this.issuer,
      aud: this.audience,
    };

    // Refresh token payload
    const refreshPayload: RefreshTokenPayload = {
      sub: userId,
      jti: sessionId,
      iat: now,
      exp: now + this.parseExpiry(config.jwt.refreshTokenExpiry),
      iss: this.issuer,
      aud: this.audience,
    };

    const accessToken = jwt.sign(accessPayload, this.accessTokenSecret, {
      algorithm: 'HS256',
    });

    const refreshToken = jwt.sign(refreshPayload, this.refreshTokenSecret, {
      algorithm: 'HS256',
    });

    return {
      accessToken,
      refreshToken,
      accessTokenExpiry: new Date(accessPayload.exp * 1000),
      refreshTokenExpiry: new Date(refreshPayload.exp * 1000),
    };
  }

  /**
   * Verify and decode access token
   */
  verifyAccessToken(token: string): AccessTokenPayload {
    try {
      const payload = jwt.verify(token, this.accessTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256'],
      }) as AccessTokenPayload;

      return payload;
    } catch (error) {
      logger.warn('Access token verification failed:', error);
      throw new Error('Invalid access token');
    }
  }

  /**
   * Verify and decode refresh token
   */
  verifyRefreshToken(token: string): RefreshTokenPayload {
    try {
      const payload = jwt.verify(token, this.refreshTokenSecret, {
        issuer: this.issuer,
        audience: this.audience,
        algorithms: ['HS256'],
      }) as RefreshTokenPayload;

      return payload;
    } catch (error) {
      logger.warn('Refresh token verification failed:', error);
      throw new Error('Invalid refresh token');
    }
  }

  /**
   * Generate new access token from refresh token
   */
  refreshAccessToken(refreshToken: string, email: string, role: string): string {
    const refreshPayload = this.verifyRefreshToken(refreshToken);
    const now = Math.floor(Date.now() / 1000);

    const accessPayload: AccessTokenPayload = {
      sub: refreshPayload.sub,
      email,
      role,
      iat: now,
      exp: now + this.parseExpiry(config.jwt.accessTokenExpiry),
      iss: this.issuer,
      aud: this.audience,
    };

    return jwt.sign(accessPayload, this.accessTokenSecret, {
      algorithm: 'HS256',
    });
  }

  /**
   * Extract token from Authorization header
   */
  extractTokenFromHeader(authHeader: string | undefined): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }

  /**
   * Get token expiry date
   */
  getTokenExpiry(token: string): Date | null {
    try {
      const decoded = jwt.decode(token) as any;
      if (decoded && decoded.exp) {
        return new Date(decoded.exp * 1000);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if token is expired
   */
  isTokenExpired(token: string): boolean {
    const expiry = this.getTokenExpiry(token);
    if (!expiry) return true;
    return expiry.getTime() < Date.now();
  }

  /**
   * Parse expiry string to seconds
   */
  private parseExpiry(expiry: string): number {
    const unit = expiry.slice(-1);
    const value = parseInt(expiry.slice(0, -1), 10);

    switch (unit) {
      case 's': return value;
      case 'm': return value * 60;
      case 'h': return value * 60 * 60;
      case 'd': return value * 24 * 60 * 60;
      default: throw new Error(`Invalid expiry format: ${expiry}`);
    }
  }
}

export const jwtService = new JWTService();

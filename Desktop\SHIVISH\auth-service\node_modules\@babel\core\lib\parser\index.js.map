{"version": 3, "names": ["_parser", "data", "require", "_codeFrame", "_missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parser", "pluginPasses", "parserOpts", "highlightCode", "filename", "code", "results", "plugins", "plugin", "parserOverride", "ast", "parse", "undefined", "push", "length", "then", "Error", "err", "message", "loc", "missingPlugin", "codeFrame", "codeFrameColumns", "start", "line", "column", "generateMissingPluginMessage"], "sources": ["../../src/parser/index.ts"], "sourcesContent": ["import type { <PERSON><PERSON> } from \"gensync\";\nimport { parse, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@babel/parser\";\nimport { codeFrameColumns } from \"@babel/code-frame\";\nimport generateMissingPluginMessage from \"./util/missing-plugin-helper.ts\";\nimport type { PluginPasses } from \"../config/index.ts\";\n\nexport type { ParseResult };\n\nexport default function* parser(\n  pluginPasses: PluginPasses,\n  { parserOpts, highlightCode = true, filename = \"unknown\" }: any,\n  code: string,\n): Handler<ParseResult> {\n  try {\n    const results = [];\n    for (const plugins of pluginPasses) {\n      for (const plugin of plugins) {\n        const { parserOverride } = plugin;\n        if (parserOverride) {\n          const ast = parserOverride(code, parserOpts, parse);\n\n          if (ast !== undefined) results.push(ast);\n        }\n      }\n    }\n\n    if (results.length === 0) {\n      return parse(code, parserOpts);\n    } else if (results.length === 1) {\n      // If we want to allow async parsers\n      yield* [];\n      if (typeof (results[0] as any).then === \"function\") {\n        throw new Error(\n          `You appear to be using an async parser plugin, ` +\n            `which your current version of Babel does not support. ` +\n            `If you're using a published plugin, you may need to upgrade ` +\n            `your @babel/core version.`,\n        );\n      }\n      return results[0];\n    }\n    // TODO: Add an error code\n    throw new Error(\"More than one plugin attempted to override parsing.\");\n  } catch (err) {\n    if (err.code === \"BABEL_PARSER_SOURCETYPE_MODULE_REQUIRED\") {\n      err.message +=\n        \"\\nConsider renaming the file to '.mjs', or setting sourceType:module \" +\n        \"or sourceType:unambiguous in your Babel config for this file.\";\n      // err.code will be changed to BABEL_PARSE_ERROR later.\n    }\n\n    const { loc, missingPlugin } = err;\n    if (loc) {\n      const codeFrame = codeFrameColumns(\n        code,\n        {\n          start: {\n            line: loc.line,\n            column: loc.column + 1,\n          },\n        },\n        {\n          highlightCode,\n        },\n      );\n      if (missingPlugin) {\n        err.message =\n          `${filename}: ` +\n          generateMissingPluginMessage(\n            missingPlugin[0],\n            loc,\n            codeFrame,\n            filename,\n          );\n      } else {\n        err.message = `${filename}: ${err.message}\\n\\n` + codeFrame;\n      }\n      err.code = \"BABEL_PARSE_ERROR\";\n    }\n    throw err;\n  }\n}\n"], "mappings": ";;;;;;AACA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,WAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,UAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,IAAAG,oBAAA,GAAAF,OAAA;AAKe,UAAUG,MAAMA,CAC7BC,YAA0B,EAC1B;EAAEC,UAAU;EAAEC,aAAa,GAAG,IAAI;EAAEC,QAAQ,GAAG;AAAe,CAAC,EAC/DC,IAAY,EACU;EACtB,IAAI;IACF,MAAMC,OAAO,GAAG,EAAE;IAClB,KAAK,MAAMC,OAAO,IAAIN,YAAY,EAAE;MAClC,KAAK,MAAMO,MAAM,IAAID,OAAO,EAAE;QAC5B,MAAM;UAAEE;QAAe,CAAC,GAAGD,MAAM;QACjC,IAAIC,cAAc,EAAE;UAClB,MAAMC,GAAG,GAAGD,cAAc,CAACJ,IAAI,EAAEH,UAAU,EAAES,eAAK,CAAC;UAEnD,IAAID,GAAG,KAAKE,SAAS,EAAEN,OAAO,CAACO,IAAI,CAACH,GAAG,CAAC;QAC1C;MACF;IACF;IAEA,IAAIJ,OAAO,CAACQ,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO,IAAAH,eAAK,EAACN,IAAI,EAAEH,UAAU,CAAC;IAChC,CAAC,MAAM,IAAII,OAAO,CAACQ,MAAM,KAAK,CAAC,EAAE;MAE/B,OAAO,EAAE;MACT,IAAI,OAAQR,OAAO,CAAC,CAAC,CAAC,CAASS,IAAI,KAAK,UAAU,EAAE;QAClD,MAAM,IAAIC,KAAK,CACb,iDAAiD,GAC/C,wDAAwD,GACxD,8DAA8D,GAC9D,2BACJ,CAAC;MACH;MACA,OAAOV,OAAO,CAAC,CAAC,CAAC;IACnB;IAEA,MAAM,IAAIU,KAAK,CAAC,qDAAqD,CAAC;EACxE,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,IAAIA,GAAG,CAACZ,IAAI,KAAK,yCAAyC,EAAE;MAC1DY,GAAG,CAACC,OAAO,IACT,uEAAuE,GACvE,+DAA+D;IAEnE;IAEA,MAAM;MAAEC,GAAG;MAAEC;IAAc,CAAC,GAAGH,GAAG;IAClC,IAAIE,GAAG,EAAE;MACP,MAAME,SAAS,GAAG,IAAAC,6BAAgB,EAChCjB,IAAI,EACJ;QACEkB,KAAK,EAAE;UACLC,IAAI,EAAEL,GAAG,CAACK,IAAI;UACdC,MAAM,EAAEN,GAAG,CAACM,MAAM,GAAG;QACvB;MACF,CAAC,EACD;QACEtB;MACF,CACF,CAAC;MACD,IAAIiB,aAAa,EAAE;QACjBH,GAAG,CAACC,OAAO,GACT,GAAGd,QAAQ,IAAI,GACf,IAAAsB,4BAA4B,EAC1BN,aAAa,CAAC,CAAC,CAAC,EAChBD,GAAG,EACHE,SAAS,EACTjB,QACF,CAAC;MACL,CAAC,MAAM;QACLa,GAAG,CAACC,OAAO,GAAG,GAAGd,QAAQ,KAAKa,GAAG,CAACC,OAAO,MAAM,GAAGG,SAAS;MAC7D;MACAJ,GAAG,CAACZ,IAAI,GAAG,mBAAmB;IAChC;IACA,MAAMY,GAAG;EACX;AACF;AAAC", "ignoreList": []}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.socialRoutes = void 0;
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const error_middleware_1 = require("../middleware/error.middleware");
const social_controller_1 = require("../controllers/social.controller");
const router = (0, express_1.Router)();
exports.socialRoutes = router;
const socialController = new social_controller_1.SocialController();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: 'Validation failed',
                details: errors.array(),
            },
        });
    }
    next();
};
const socialLoginValidation = [
    (0, express_validator_1.body)('accessToken')
        .notEmpty()
        .withMessage('Access token is required'),
    (0, express_validator_1.body)('deviceInfo')
        .optional()
        .isObject()
        .withMessage('Device info must be an object'),
];
router.post('/google', socialLoginValidation, validateRequest, (0, error_middleware_1.asyncHandler)(socialController.googleLogin));
router.post('/apple', socialLoginValidation, validateRequest, (0, error_middleware_1.asyncHandler)(socialController.appleLogin));
//# sourceMappingURL=social.routes.js.map
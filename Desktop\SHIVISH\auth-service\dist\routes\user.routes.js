"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRoutes = void 0;
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const error_middleware_1 = require("../middleware/error.middleware");
const auth_middleware_1 = require("../middleware/auth.middleware");
const user_controller_1 = require("../controllers/user.controller");
const router = (0, express_1.Router)();
exports.userRoutes = router;
const userController = new user_controller_1.UserController();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: 'Validation failed',
                details: errors.array(),
            },
        });
    }
    next();
};
const updateProfileValidation = [
    (0, express_validator_1.body)('displayName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Display name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('phoneNumber')
        .optional()
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number'),
    (0, express_validator_1.body)('photoUrl')
        .optional()
        .isURL()
        .withMessage('Please provide a valid photo URL'),
];
const changePasswordValidation = [
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
];
router.use(auth_middleware_1.authMiddleware);
router.get('/profile', (0, error_middleware_1.asyncHandler)(userController.getProfile));
router.put('/profile', updateProfileValidation, validateRequest, (0, error_middleware_1.asyncHandler)(userController.updateProfile));
router.put('/change-password', changePasswordValidation, validateRequest, (0, error_middleware_1.asyncHandler)(userController.changePassword));
router.delete('/account', (0, error_middleware_1.asyncHandler)(userController.deleteAccount));
router.get('/sessions', (0, error_middleware_1.asyncHandler)(userController.getSessions));
router.delete('/sessions/:sessionId', (0, error_middleware_1.asyncHandler)(userController.deleteSession));
//# sourceMappingURL=user.routes.js.map
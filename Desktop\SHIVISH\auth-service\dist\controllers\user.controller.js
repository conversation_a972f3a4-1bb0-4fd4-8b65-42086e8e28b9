"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const user_service_1 = require("../services/user.service");
const logger_1 = require("../utils/logger");
class UserController {
    constructor() {
        this.getProfile = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                const user = await this.userService.getUserById(userId);
                res.json({
                    success: true,
                    data: user,
                });
            }
            catch (error) {
                logger_1.logger.error('Get profile error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'GET_PROFILE_FAILED',
                        message: error.message || 'Failed to get user profile',
                    },
                });
            }
        };
        this.updateProfile = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                const { displayName, phoneNumber, photoUrl } = req.body;
                const updatedUser = await this.userService.updateProfile(userId, {
                    displayName,
                    phoneNumber,
                    photoUrl,
                });
                res.json({
                    success: true,
                    data: updatedUser,
                    message: 'Profile updated successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Update profile error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'UPDATE_PROFILE_FAILED',
                        message: error.message || 'Failed to update profile',
                    },
                });
            }
        };
        this.changePassword = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                const { currentPassword, newPassword } = req.body;
                await this.userService.changePassword(userId, currentPassword, newPassword);
                res.json({
                    success: true,
                    message: 'Password changed successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Change password error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'CHANGE_PASSWORD_FAILED',
                        message: error.message || 'Failed to change password',
                    },
                });
            }
        };
        this.deleteAccount = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                await this.userService.deleteAccount(userId);
                res.json({
                    success: true,
                    message: 'Account deleted successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Delete account error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'DELETE_ACCOUNT_FAILED',
                        message: error.message || 'Failed to delete account',
                    },
                });
            }
        };
        this.getSessions = async (req, res) => {
            try {
                const userId = req.user?.id;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                const sessions = await this.userService.getUserSessions(userId);
                res.json({
                    success: true,
                    data: sessions,
                });
            }
            catch (error) {
                logger_1.logger.error('Get sessions error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'GET_SESSIONS_FAILED',
                        message: error.message || 'Failed to get user sessions',
                    },
                });
            }
        };
        this.deleteSession = async (req, res) => {
            try {
                const userId = req.user?.id;
                const { sessionId } = req.params;
                if (!userId) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'UNAUTHORIZED',
                            message: 'User not authenticated',
                        },
                    });
                    return;
                }
                await this.userService.deleteSession(userId, sessionId);
                res.json({
                    success: true,
                    message: 'Session deleted successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Delete session error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'DELETE_SESSION_FAILED',
                        message: error.message || 'Failed to delete session',
                    },
                });
            }
        };
        this.userService = new user_service_1.UserService();
    }
}
exports.UserController = UserController;
//# sourceMappingURL=user.controller.js.map
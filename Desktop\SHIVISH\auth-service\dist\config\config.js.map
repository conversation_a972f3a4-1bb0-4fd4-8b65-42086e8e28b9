{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../src/config/config.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAG5B,gBAAM,CAAC,MAAM,EAAE,CAAC;AA8DhB,MAAM,MAAM,GAAW;IACrB,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;IAE1C,MAAM,EAAE;QACN,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,MAAM,EAAE,EAAE,CAAC;QAC9C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,WAAW;KACtC;IAED,QAAQ,EAAE;QACR,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;QACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE,CAAC;QACjD,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,cAAc;QAC3C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,UAAU;QACvC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,EAAE;QACvC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,MAAM;KACnC;IAED,KAAK,EAAE;QACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,WAAW;QAC3C,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,MAAM,EAAE,EAAE,CAAC;QACpD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;QACpC,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,EAAE,EAAE,CAAC;KAC9C;IAED,GAAG,EAAE;QACH,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,mDAAmD;QACvG,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,oDAAoD;QAC1G,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,KAAK;QACzD,kBAAkB,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,KAAK;QAC3D,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sBAAsB;QACxD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,aAAa;KACpD;IAED,QAAQ,EAAE;QACR,UAAU,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,IAAI,EAAE,EAAE,CAAC;QACzD,gBAAgB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,GAAG,EAAE,EAAE,CAAC;QACrE,eAAe,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,IAAI,EAAE,EAAE,CAAC;QACnE,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI,GAAG,EAAE,EAAE,CAAC;QACvE,0BAA0B,EAAE,OAAO,CAAC,GAAG,CAAC,wBAAwB,KAAK,MAAM;KAC5E;IAED,KAAK,EAAE;QACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,gBAAgB;QAChD,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,KAAK,EAAE,EAAE,CAAC;QACnD,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,KAAK,MAAM;QAC3C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;QAClC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE;QAC1C,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,qBAAqB;KACtD;IAED,IAAI,EAAE;QACJ,cAAc,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;YACtC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC;YACrC,CAAC,CAAC,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;KACvD;IAED,MAAM,EAAE;QACN,MAAM,EAAE;YACN,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,EAAE;YAC5C,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,EAAE;SACrD;QACD,KAAK,EAAE;YACL,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,EAAE;YAC3C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,EAAE;YACvC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE;YACrC,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,EAAE;SAChD;KACF;CACF,CAAC;AAoBO,wBAAM;AAjBf,MAAM,eAAe,GAAG;IACtB,mBAAmB;IACnB,oBAAoB;IACpB,SAAS;IACT,SAAS;IACT,SAAS;IACT,aAAa;CACd,CAAC;AAEF,IAAI,MAAM,CAAC,GAAG,KAAK,YAAY,EAAE,CAAC;IAChC,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;QACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,aAAa,CAAC,CAAC;QACxE,CAAC;IACH,CAAC;AACH,CAAC"}
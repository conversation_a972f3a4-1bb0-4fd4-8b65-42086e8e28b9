"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authRoutes = void 0;
const express_1 = require("express");
const express_validator_1 = require("express-validator");
const error_middleware_1 = require("../middleware/error.middleware");
const auth_controller_1 = require("../controllers/auth.controller");
const router = (0, express_1.Router)();
exports.authRoutes = router;
const authController = new auth_controller_1.AuthController();
const validateRequest = (req, res, next) => {
    const errors = (0, express_validator_1.validationResult)(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            success: false,
            error: {
                code: 'VALIDATION_ERROR',
                message: 'Validation failed',
                details: errors.array(),
            },
        });
    }
    next();
};
const registerValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
    (0, express_validator_1.body)('displayName')
        .optional()
        .isLength({ min: 2, max: 50 })
        .withMessage('Display name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('phoneNumber')
        .optional()
        .isMobilePhone('any')
        .withMessage('Please provide a valid phone number'),
    (0, express_validator_1.body)('role')
        .optional()
        .isIn(['buyer', 'seller', 'priest', 'technician', 'executor', 'admin'])
        .withMessage('Invalid role specified'),
];
const loginValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('password')
        .notEmpty()
        .withMessage('Password is required'),
];
const forgotPasswordValidation = [
    (0, express_validator_1.body)('email')
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
];
const resetPasswordValidation = [
    (0, express_validator_1.body)('token')
        .notEmpty()
        .withMessage('Reset token is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
];
const changePasswordValidation = [
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 8 })
        .withMessage('Password must be at least 8 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('Password must contain at least one uppercase letter, one lowercase letter, and one number'),
];
router.post('/register', registerValidation, validateRequest, (0, error_middleware_1.asyncHandler)(authController.register));
router.post('/login', loginValidation, validateRequest, (0, error_middleware_1.asyncHandler)(authController.login));
router.post('/refresh', (0, error_middleware_1.asyncHandler)(authController.refreshToken));
router.post('/logout', (0, error_middleware_1.asyncHandler)(authController.logout));
router.post('/forgot-password', forgotPasswordValidation, validateRequest, (0, error_middleware_1.asyncHandler)(authController.forgotPassword));
router.post('/reset-password', resetPasswordValidation, validateRequest, (0, error_middleware_1.asyncHandler)(authController.resetPassword));
router.post('/verify-email', (0, error_middleware_1.asyncHandler)(authController.verifyEmail));
router.post('/resend-verification', (0, error_middleware_1.asyncHandler)(authController.resendVerification));
//# sourceMappingURL=auth.routes.js.map
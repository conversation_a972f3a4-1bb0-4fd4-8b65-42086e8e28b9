"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.passwordService = exports.PasswordService = void 0;
const argon2_1 = __importDefault(require("argon2"));
const crypto_1 = __importDefault(require("crypto"));
const config_1 = require("../config/config");
const logger_1 = require("../utils/logger");
class PasswordService {
    constructor() {
        this.saltLength = 32;
        this.hashOptions = {
            type: argon2_1.default.argon2id,
            memoryCost: 2 ** 16,
            timeCost: 3,
            parallelism: 1,
        };
    }
    async hashPassword(password) {
        try {
            const hash = await argon2_1.default.hash(password, this.hashOptions);
            logger_1.logger.debug('Password hashed successfully');
            return hash;
        }
        catch (error) {
            logger_1.logger.error('Password hashing failed:', error);
            throw new Error('Failed to hash password');
        }
    }
    async verifyPassword(password, hash) {
        try {
            const isValid = await argon2_1.default.verify(hash, password);
            logger_1.logger.debug(`Password verification: ${isValid ? 'success' : 'failed'}`);
            return isValid;
        }
        catch (error) {
            logger_1.logger.error('Password verification failed:', error);
            return false;
        }
    }
    validatePassword(password) {
        const errors = [];
        const minLength = config_1.config.security.passwordMinLength;
        const requireSpecialChar = config_1.config.security.passwordRequireSpecialChar;
        if (password.length < minLength) {
            errors.push(`Password must be at least ${minLength} characters long`);
        }
        if (!/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (requireSpecialChar && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }
        if (this.hasCommonPatterns(password)) {
            errors.push('Password contains common patterns and is not secure');
        }
        return {
            isValid: errors.length === 0,
            errors,
        };
    }
    generateSecurePassword(length = 16) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        let password = '';
        password += this.getRandomChar('abcdefghijklmnopqrstuvwxyz');
        password += this.getRandomChar('ABCDEFGHIJKLMNOPQRSTUVWXYZ');
        password += this.getRandomChar('0123456789');
        password += this.getRandomChar('!@#$%^&*');
        for (let i = password.length; i < length; i++) {
            password += this.getRandomChar(charset);
        }
        return password.split('').sort(() => Math.random() - 0.5).join('');
    }
    generateResetToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    generateVerificationToken() {
        return crypto_1.default.randomBytes(32).toString('hex');
    }
    hasCommonPatterns(password) {
        const commonPatterns = [
            /123456/,
            /password/i,
            /qwerty/i,
            /abc123/i,
            /admin/i,
            /letmein/i,
            /welcome/i,
            /monkey/i,
            /dragon/i,
            /master/i,
        ];
        return commonPatterns.some(pattern => pattern.test(password));
    }
    getRandomChar(charset) {
        const randomIndex = crypto_1.default.randomInt(0, charset.length);
        return charset[randomIndex];
    }
    calculatePasswordStrength(password) {
        let score = 0;
        score += Math.min(password.length * 4, 25);
        if (/[a-z]/.test(password))
            score += 5;
        if (/[A-Z]/.test(password))
            score += 5;
        if (/\d/.test(password))
            score += 5;
        if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password))
            score += 10;
        const uniqueChars = new Set(password).size;
        score += Math.min(uniqueChars * 2, 20);
        if (this.hasCommonPatterns(password))
            score -= 20;
        if (/(.)\1{2,}/.test(password))
            score -= 10;
        if (/012|123|234|345|456|567|678|789|890/.test(password))
            score -= 10;
        return Math.max(0, Math.min(100, score));
    }
    getPasswordStrengthDescription(score) {
        if (score < 30)
            return 'Very Weak';
        if (score < 50)
            return 'Weak';
        if (score < 70)
            return 'Fair';
        if (score < 90)
            return 'Good';
        return 'Excellent';
    }
}
exports.PasswordService = PasswordService;
exports.passwordService = new PasswordService();
//# sourceMappingURL=password.service.js.map
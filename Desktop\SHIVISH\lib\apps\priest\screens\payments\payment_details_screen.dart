import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/priest/presentation/cubits/priest_cubit.dart';
import 'package:shivish/shared/models/payment/payment_model.dart';
import 'package:shivish/shared/models/priest.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';

class PaymentDetailsScreen extends StatelessWidget {
  final String paymentId;

  const PaymentDetailsScreen({
    super.key,
    required this.paymentId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const AppToolbar(
        title: 'Payment Details',
      ),
      body: BlocBuilder<PriestCubit, PriestState>(
        builder: (context, state) {
          return state.maybeWhen(
            loading: () => const LoadingIndicator(),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            loaded: (priest) => _buildPaymentDetails(context, priest),
            orElse: () => const Center(child: Text('Unknown State')),
          );
        },
      ),
    );
  }

  Widget _buildPaymentDetails(BuildContext context, Priest priest) {
    final payment = priest.payments.firstWhere(
      (p) => p.id == paymentId,
      orElse: () => throw Exception('Payment not found'),
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Payment #${payment.paymentNumber}',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: payment.status == PaymentStatus.completed
                              ? Colors.green.withOpacity(0.1)
                              : Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          payment.status.name.toUpperCase(),
                          style: TextStyle(
                            color: payment.status == PaymentStatus.completed
                                ? Colors.green
                                : Colors.grey,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    context,
                    'Order ID',
                    payment.orderId,
                  ),
                  _buildDetailRow(
                    context,
                    'Customer ID',
                    payment.customerId,
                  ),
                  _buildDetailRow(
                    context,
                    'Payment Method',
                    payment.method.name.toUpperCase(),
                  ),
                  _buildDetailRow(
                    context,
                    'Payment Gateway',
                    payment.gateway.name.toUpperCase(),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Amount Details',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    context,
                    'Amount',
                    '₹${payment.amount.toStringAsFixed(2)}',
                  ),
                  _buildDetailRow(
                    context,
                    'Tax',
                    '₹${payment.taxAmount.toStringAsFixed(2)}',
                  ),
                  _buildDetailRow(
                    context,
                    'Fee',
                    '₹${payment.feeAmount.toStringAsFixed(2)}',
                  ),
                  const Divider(height: 32),
                  _buildDetailRow(
                    context,
                    'Total Amount',
                    '₹${payment.totalAmount.toStringAsFixed(2)}',
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Timestamps',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    context,
                    'Created At',
                    _formatDateTime(payment.createdAt),
                  ),
                  _buildDetailRow(
                    context,
                    'Updated At',
                    _formatDateTime(payment.updatedAt),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: isTotal ? FontWeight.bold : null,
                  color: isTotal ? Theme.of(context).colorScheme.primary : null,
                ),
          ),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute}';
  }
}

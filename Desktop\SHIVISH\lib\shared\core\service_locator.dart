import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:get_it/get_it.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shivish/shared/auth/services/auth_service.dart';
import 'package:shivish/shared/auth/models/auth_models.dart';
import 'package:shivish/shared/auth/providers/custom_auth_provider.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/auth/auth_service.dart';
import '../services/firestore_service.dart';
import '../services/storage/storage_service.dart';
import '../services/analytics/analytics_service.dart';
import '../services/messaging_service.dart';
import '../core/auth/repositories/auth_repository.dart';
import '../services/product/product_service.dart';
import '../services/review/review_service.dart';
import '../services/executor_service.dart';
import '../services/seller_service.dart';
import '../services/priest_service.dart';
import '../services/technician_service.dart';
import '../../apps/admin/bloc/executor/executor_bloc.dart';
import '../../apps/admin/bloc/priest/priest_bloc.dart';
import '../../apps/admin/bloc/product/product_bloc.dart';
import '../../apps/admin/bloc/seller/seller_bloc.dart';
import '../../apps/admin/bloc/technician/technician_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import '../../apps/admin/bloc/analytics/analytics_event.dart';
import '../../apps/admin/bloc/analytics/analytics_state.dart';
import '../services/commission/commission_service.dart';
import '../../apps/admin/bloc/commission/commission_bloc.dart';
import '../services/payment_gateway/payment_gateway_service.dart';
import '../services/payment_gateway/payment_gateway_bloc.dart';
import '../services/notification_service.dart';
import '../services/mock_notification_service.dart';
import '../services/debug_service.dart';
import 'package:shivish/shared/services/language_service.dart';
import '../services/payment/biometric_auth_service.dart';
import '../services/backup/hybrid_backup_service.dart';
import '../services/backup/backup_scheduler.dart';
import '../services/backup/backup_config_manager.dart';
import '../services/backup/backup_restore_service.dart';
import '../services/backup/backup_encryption_service.dart';
import '../services/backup_service.dart';
import '../services/location/location_service.dart';
import '../services/system_config_service.dart';
import '../repositories/system_config_repository.dart';
import '../services/tax/tax_update_service.dart';
import '../services/storage/adaptive_storage_service.dart';
import '../services/storage/storage_config_listener.dart';

final GetIt serviceLocator = GetIt.instance;

// Global navigator key for accessing context from anywhere
final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

@InjectableInit()
Future<void> setupServiceLocator() async {
  // Register Custom Auth Provider as singleton
  if (!serviceLocator.isRegistered<CustomAuthProvider>()) {
    serviceLocator
        .registerLazySingleton<CustomAuthProvider>(() => CustomAuthProvider());
  }
  if (!serviceLocator.isRegistered<GoogleSignIn>()) {
    serviceLocator.registerLazySingleton<GoogleSignIn>(() => GoogleSignIn());
  }
  if (!serviceLocator.isRegistered<FirebaseFirestore>()) {
    serviceLocator.registerLazySingleton<FirebaseFirestore>(
        () => FirebaseFirestore.instance);
  }
  if (!serviceLocator.isRegistered<FirebaseStorage>()) {
    serviceLocator
        .registerLazySingleton<FirebaseStorage>(() => FirebaseStorage.instance);
  }

  // Register a mock NotificationService for the HomeScreen
  if (!serviceLocator.isRegistered<NotificationService>()) {
    serviceLocator.registerLazySingleton<NotificationService>(() {
      // Create a mock implementation that doesn't require a GoRouter
      return MockNotificationService();
    });
    debugPrint('Mock NotificationService registered successfully');
  }

  // Register DebugService
  if (!serviceLocator.isRegistered<DebugService>()) {
    serviceLocator.registerLazySingleton<DebugService>(() => DebugService());
  }

  // Register SharedPreferences
  final prefs = await SharedPreferences.getInstance();
  if (!serviceLocator.isRegistered<SharedPreferences>()) {
    serviceLocator.registerSingleton<SharedPreferences>(prefs);
  }

  // Register language service
  final languageService = await LanguageService.initialize();
  if (!serviceLocator.isRegistered<LanguageService>()) {
    serviceLocator.registerSingleton<LanguageService>(languageService);
  }

  // Register services as singletons
  if (!serviceLocator.isRegistered<AuthService>()) {
    serviceLocator.registerLazySingleton<AuthService>(() => AuthService());
  }
  if (!serviceLocator.isRegistered<FirestoreService>()) {
    serviceLocator
        .registerLazySingleton<FirestoreService>(() => FirestoreService());
  }
  if (!serviceLocator.isRegistered<StorageService>()) {
    serviceLocator.registerLazySingleton<StorageService>(
      () => StorageService(FirebaseStorage.instance),
    );
  }

  // Register adaptive storage service
  if (!serviceLocator.isRegistered<AdaptiveStorageService>()) {
    final adaptiveStorageService = AdaptiveStorageService();
    // Initialize the service
    adaptiveStorageService.initialize();
    serviceLocator.registerSingleton<AdaptiveStorageService>(adaptiveStorageService);
  }

  // Register storage config listener
  if (!serviceLocator.isRegistered<StorageConfigListener>()) {
    final storageConfigListener = StorageConfigListener(null);
    storageConfigListener.initialize();
    serviceLocator.registerSingleton<StorageConfigListener>(storageConfigListener);
  }
  if (!serviceLocator.isRegistered<AnalyticsService>()) {
    serviceLocator.registerSingleton<AnalyticsService>(
        AnalyticsService(serviceLocator<FirebaseFirestore>()));
  }
  if (!serviceLocator.isRegistered<MessagingService>()) {
    serviceLocator
        .registerLazySingleton<MessagingService>(() => MessagingService());
  }

  if (!serviceLocator.isRegistered<AuthRepository>()) {
    serviceLocator.registerSingleton<AuthRepository>(AuthRepository());
  }
  if (!serviceLocator.isRegistered<ProductService>()) {
    serviceLocator
        .registerLazySingleton<ProductService>(() => ProductService());
  }
  if (!serviceLocator.isRegistered<ReviewService>()) {
    serviceLocator.registerLazySingleton<ReviewService>(
      () => ReviewService(),
    );
  }

  // Register ExecutorService and ExecutorBloc
  if (!serviceLocator.isRegistered<ExecutorService>()) {
    serviceLocator.registerLazySingleton<ExecutorService>(
      () => ExecutorService(
        serviceLocator<FirebaseFirestore>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }
  if (!serviceLocator.isRegistered<ExecutorBloc>()) {
    serviceLocator.registerFactory<ExecutorBloc>(
      () => ExecutorBloc(serviceLocator<ExecutorService>()),
    );
  }

  // Register PriestService and PriestBloc
  if (!serviceLocator.isRegistered<PriestService>()) {
    serviceLocator.registerSingleton<PriestService>(
      PriestService(
        serviceLocator<FirebaseFirestore>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }
  if (!serviceLocator.isRegistered<PriestBloc>()) {
    serviceLocator.registerFactory<PriestBloc>(
      () => PriestBloc(serviceLocator<PriestService>()),
    );
  }

  // Register ProductBloc
  if (!serviceLocator.isRegistered<ProductBloc>()) {
    serviceLocator.registerFactory<ProductBloc>(
      () => ProductBloc(serviceLocator<ProductService>()),
    );
  }

  // Register SellerService
  if (!serviceLocator.isRegistered<SellerService>()) {
    serviceLocator.registerLazySingleton<SellerService>(
      () => SellerService(
        serviceLocator<FirebaseFirestore>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register SellerBloc
  if (!serviceLocator.isRegistered<SellerBloc>()) {
    serviceLocator.registerFactory<SellerBloc>(
      () => SellerBloc(serviceLocator<SellerService>()),
    );
  }

  // Register TechnicianService if not already registered
  if (!serviceLocator.isRegistered<TechnicianService>()) {
    serviceLocator.registerLazySingleton<TechnicianService>(
      () => TechnicianService(
        serviceLocator<FirebaseFirestore>(),
        serviceLocator<SharedPreferences>(),
      ),
    );
  }

  // Register TechnicianBloc
  if (!serviceLocator.isRegistered<TechnicianBloc>()) {
    serviceLocator.registerFactory<TechnicianBloc>(
      () => TechnicianBloc(serviceLocator<TechnicianService>()),
    );
  }

  // Register backup services
  if (!serviceLocator.isRegistered<BackupService>()) {
    serviceLocator.registerLazySingleton<BackupService>(
      () => BackupService(serviceLocator<FirebaseFirestore>()),
    );
  }

  if (!serviceLocator.isRegistered<BackupEncryptionService>()) {
    serviceLocator.registerLazySingleton<BackupEncryptionService>(
      () => BackupEncryptionService(),
    );
  }

  if (!serviceLocator.isRegistered<HybridBackupService>()) {
    serviceLocator.registerLazySingleton<HybridBackupService>(
      () => HybridBackupService(
        encryptionService: serviceLocator<BackupEncryptionService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<BackupRestoreService>()) {
    serviceLocator.registerLazySingleton<BackupRestoreService>(
      () => BackupRestoreService(
        encryptionService: serviceLocator<BackupEncryptionService>(),
      ),
    );
  }

  if (!serviceLocator.isRegistered<BackupScheduler>()) {
    serviceLocator.registerLazySingleton<BackupScheduler>(
      () => BackupScheduler(serviceLocator<HybridBackupService>()),
    );
  }

  if (!serviceLocator.isRegistered<BackupConfigManager>()) {
    serviceLocator.registerLazySingleton<BackupConfigManager>(
      () => BackupConfigManager(),
    );
  }

  // Register LocationService
  if (!serviceLocator.isRegistered<LocationService>()) {
    serviceLocator.registerLazySingleton<LocationService>(
      () => LocationService(),
    );
  }

  // Register SystemConfigService
  if (!serviceLocator.isRegistered<SystemConfigService>()) {
    serviceLocator.registerLazySingleton<SystemConfigService>(() {
      final repository = SystemConfigRepository(serviceLocator<FirebaseFirestore>());
      return SystemConfigService(repository);
    });
  }

  // Register PaymentGatewayService
  if (!serviceLocator.isRegistered<PaymentGatewayService>()) {
    serviceLocator.registerLazySingleton<PaymentGatewayService>(() {
      return PaymentGatewayService(serviceLocator<FirebaseFirestore>());
    });
  }

  // BackupController registration is temporarily commented out
  // until the controller is properly implemented
  /*
  if (!serviceLocator.isRegistered<BackupController>()) {
    serviceLocator.registerLazySingleton<BackupController>(
      () => BackupController(
        backupService: serviceLocator<HybridBackupService>(),
        scheduler: serviceLocator<BackupScheduler>(),
        configManager: serviceLocator<BackupConfigManager>(),
        restoreService: serviceLocator<BackupRestoreService>(),
      ),
    );
  }
  */

  // Initialize services that require async initialization
  try {
    final messagingService = serviceLocator<MessagingService>();
    await messagingService.initialize();
    debugPrint('MessagingService initialized successfully');
  } catch (e) {
    debugPrint('MessagingService initialization failed: $e');
    // Continue without messaging service
  }

  // Initialize biometric authentication service
  try {
    debugPrint('Initializing BiometricAuthService...');
    await BiometricAuthService.initialize();
    debugPrint('BiometricAuthService initialized');
  } catch (e) {
    debugPrint('BiometricAuthService initialization failed: $e');
    // Continue without biometric authentication service
  }

  // Initialize location service
  try {
    debugPrint('Initializing LocationService...');
    final locationService = serviceLocator<LocationService>();
    await locationService.initialize();
    debugPrint('LocationService initialized');
  } catch (e) {
    debugPrint('LocationService initialization failed: $e');
    // Continue without location service
  }

  // Initialize tax update service
  try {
    debugPrint('Initializing TaxUpdateService...');
    final taxUpdateService = TaxUpdateService();
    await taxUpdateService.initialize();
    taxUpdateService.start();
    serviceLocator.registerSingleton<TaxUpdateService>(taxUpdateService);
    debugPrint('TaxUpdateService initialized and started');
  } catch (e) {
    debugPrint('TaxUpdateService initialization failed: $e');
    // Continue without tax update service
  }
}

// Extension method for easier access to services
extension ServiceLocatorExtension on GetIt {
  CustomAuthProvider get customAuth => get<CustomAuthProvider>();
  AuthService get auth => get<AuthService>();
  FirestoreService get firestore => get<FirestoreService>();
  StorageService get storage => get<StorageService>();
  AnalyticsService get analytics => get<AnalyticsService>();
  MessagingService get messaging => get<MessagingService>();
  AuthRepository get authRepository => get<AuthRepository>();
  ProductService get product => get<ProductService>();
  ReviewService get review => get<ReviewService>();
  ExecutorService get executor => get<ExecutorService>();
  ExecutorBloc get executorBloc => get<ExecutorBloc>();
  PriestService get priest => get<PriestService>();
  PriestBloc get priestBloc => get<PriestBloc>();
  SellerService get seller => get<SellerService>();
  SellerBloc get sellerBloc => get<SellerBloc>();
  TechnicianService get technician => get<TechnicianService>();
  TechnicianBloc get technicianBloc => get<TechnicianBloc>();
  DebugService get debug => get<DebugService>();
  LocationService get location => get<LocationService>();
  SystemConfigService get systemConfig => get<SystemConfigService>();
  PaymentGatewayService get paymentGateway => get<PaymentGatewayService>();
  BackupService get backup => get<BackupService>();
}

// Analytics
// Temporarily commenting out to fix duplicate registration
// @module
abstract class AnalyticsModule {
  // @singleton
  AnalyticsService get analyticsService =>
      AnalyticsService(serviceLocator<FirebaseFirestore>());

  // @singleton
  AnalyticsBloc get analyticsBloc => AnalyticsBloc(analyticsService);
}

// Temporarily commenting out to fix duplicate registration
// @singleton
class AnalyticsBloc extends Bloc<AnalyticsEvent, AnalyticsState> {
  final AnalyticsService _analyticsService;

  AnalyticsBloc(this._analyticsService)
      : super(const AnalyticsState.initial()) {
    on<LoadAnalyticsData>(_onLoadAnalyticsData);
    on<UpdateTimeRange>(_onUpdateTimeRange);
    on<RefreshAnalyticsData>(_onRefreshAnalyticsData);
    on<ApplyFilters>(_onApplyFilters);
  }

  Future<void> _onLoadAnalyticsData(
    LoadAnalyticsData event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      emit(const AnalyticsState.loading());
      final data = await _analyticsService.getAnalyticsData(event.timeRange);
      emit(AnalyticsState.loaded(data, timeRange: event.timeRange));
    } catch (e) {
      emit(AnalyticsState.error(e.toString()));
    }
  }

  Future<void> _onUpdateTimeRange(
    UpdateTimeRange event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      emit(const AnalyticsState.loading());
      final data = await _analyticsService.getAnalyticsData(event.timeRange);
      emit(AnalyticsState.loaded(data, timeRange: event.timeRange));
    } catch (e) {
      emit(AnalyticsState.error(e.toString()));
    }
  }

  Future<void> _onRefreshAnalyticsData(
    RefreshAnalyticsData event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final currentState = state;
      currentState.maybeWhen(
        loaded: (data, timeRange, filters) async {
          emit(const AnalyticsState.loading());
          final refreshedData =
              await _analyticsService.getAnalyticsData(timeRange);
          emit(AnalyticsState.loaded(refreshedData,
              timeRange: timeRange, filters: filters));
        },
        orElse: () {},
      );
    } catch (e) {
      emit(AnalyticsState.error(e.toString()));
    }
  }

  Future<void> _onApplyFilters(
    ApplyFilters event,
    Emitter<AnalyticsState> emit,
  ) async {
    try {
      final currentState = state;
      currentState.maybeWhen(
        loaded: (data, timeRange, _) async {
          emit(const AnalyticsState.loading());
          final filteredData =
              await _analyticsService.getAnalyticsData(timeRange);
          emit(AnalyticsState.loaded(filteredData,
              timeRange: timeRange, filters: event.filters));
        },
        orElse: () {},
      );
    } catch (e) {
      emit(AnalyticsState.error(e.toString()));
    }
  }
}

// Temporarily commenting out to fix duplicate registration
// @module
abstract class RegisterModule {
  // @injectable
  CommissionService commissionService(FirebaseFirestore firestore) =>
      CommissionService(firestore);

  // @injectable
  CommissionBloc commissionBloc(CommissionService service) =>
      CommissionBloc(service);

  @injectable
  PaymentGatewayService paymentGatewayService(FirebaseFirestore firestore) =>
      PaymentGatewayService(firestore);

  @injectable
  PaymentGatewayBloc paymentGatewayBloc(PaymentGatewayService service) =>
      PaymentGatewayBloc(service);
}

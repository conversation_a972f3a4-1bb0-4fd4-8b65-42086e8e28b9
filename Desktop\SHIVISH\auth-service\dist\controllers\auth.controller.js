"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const auth_service_1 = require("../services/auth.service");
const logger_1 = require("../utils/logger");
class AuthController {
    constructor() {
        this.register = async (req, res) => {
            try {
                const { email, password, displayName, phoneNumber, role, deviceInfo } = req.body;
                const result = await this.authService.register({
                    email,
                    password,
                    displayName,
                    phoneNumber,
                    role: role || 'buyer',
                    deviceInfo,
                    ipAddress: req.ip,
                });
                res.status(201).json({
                    success: true,
                    data: result,
                    message: 'User registered successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Registration error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'REGISTRATION_FAILED',
                        message: error.message || 'Registration failed',
                    },
                });
            }
        };
        this.login = async (req, res) => {
            try {
                const { email, password, deviceInfo } = req.body;
                const result = await this.authService.login({
                    email,
                    password,
                    deviceInfo,
                    ipAddress: req.ip,
                });
                res.cookie('refreshToken', result.tokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'strict',
                    maxAge: 30 * 24 * 60 * 60 * 1000,
                });
                res.json({
                    success: true,
                    data: {
                        user: result.user,
                        accessToken: result.tokens.accessToken,
                        expiresAt: result.tokens.expiresAt,
                    },
                    message: 'Login successful',
                });
            }
            catch (error) {
                logger_1.logger.error('Login error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'LOGIN_FAILED',
                        message: error.message || 'Login failed',
                    },
                });
            }
        };
        this.refreshToken = async (req, res) => {
            try {
                const refreshToken = req.body.refreshToken || req.cookies.refreshToken;
                if (!refreshToken) {
                    res.status(401).json({
                        success: false,
                        error: {
                            code: 'MISSING_REFRESH_TOKEN',
                            message: 'Refresh token is required',
                        },
                    });
                    return;
                }
                const result = await this.authService.refreshToken(refreshToken);
                res.json({
                    success: true,
                    data: {
                        accessToken: result.accessToken,
                        expiresAt: result.expiresAt,
                    },
                    message: 'Token refreshed successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Token refresh error:', error);
                res.status(error.statusCode || 401).json({
                    success: false,
                    error: {
                        code: error.code || 'TOKEN_REFRESH_FAILED',
                        message: error.message || 'Token refresh failed',
                    },
                });
            }
        };
        this.logout = async (req, res) => {
            try {
                const refreshToken = req.body.refreshToken || req.cookies.refreshToken;
                if (refreshToken) {
                    await this.authService.logout(refreshToken);
                }
                res.clearCookie('refreshToken');
                res.json({
                    success: true,
                    message: 'Logout successful',
                });
            }
            catch (error) {
                logger_1.logger.error('Logout error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'LOGOUT_FAILED',
                        message: error.message || 'Logout failed',
                    },
                });
            }
        };
        this.forgotPassword = async (req, res) => {
            try {
                const { email } = req.body;
                await this.authService.forgotPassword(email);
                res.json({
                    success: true,
                    message: 'Password reset instructions sent to your email',
                });
            }
            catch (error) {
                logger_1.logger.error('Forgot password error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'FORGOT_PASSWORD_FAILED',
                        message: error.message || 'Failed to process password reset request',
                    },
                });
            }
        };
        this.resetPassword = async (req, res) => {
            try {
                const { token, newPassword } = req.body;
                await this.authService.resetPassword(token, newPassword);
                res.json({
                    success: true,
                    message: 'Password reset successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Reset password error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'RESET_PASSWORD_FAILED',
                        message: error.message || 'Password reset failed',
                    },
                });
            }
        };
        this.verifyEmail = async (req, res) => {
            try {
                const { token } = req.body;
                await this.authService.verifyEmail(token);
                res.json({
                    success: true,
                    message: 'Email verified successfully',
                });
            }
            catch (error) {
                logger_1.logger.error('Email verification error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'EMAIL_VERIFICATION_FAILED',
                        message: error.message || 'Email verification failed',
                    },
                });
            }
        };
        this.resendVerification = async (req, res) => {
            try {
                const { email } = req.body;
                await this.authService.resendVerification(email);
                res.json({
                    success: true,
                    message: 'Verification email sent',
                });
            }
            catch (error) {
                logger_1.logger.error('Resend verification error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'RESEND_VERIFICATION_FAILED',
                        message: error.message || 'Failed to resend verification email',
                    },
                });
            }
        };
        this.authService = new auth_service_1.AuthService();
    }
}
exports.AuthController = AuthController;
//# sourceMappingURL=auth.controller.js.map
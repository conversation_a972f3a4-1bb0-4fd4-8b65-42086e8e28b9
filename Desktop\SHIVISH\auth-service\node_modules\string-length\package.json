{"name": "string-length", "version": "4.0.2", "description": "Get the real length of a string - by correctly counting astral symbols and ignoring ansi escape codes", "license": "MIT", "repository": "sindresorhus/string-length", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["unicode", "string", "length", "size", "count", "astral", "symbol", "surrogates", "codepoints", "ansi", "escape", "codes"], "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "devDependencies": {"ava": "^3.1.0", "tsd": "^0.11.0", "xo": "^0.25.3"}}
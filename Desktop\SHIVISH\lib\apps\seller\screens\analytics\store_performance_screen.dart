import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/performance/store_performance_cubit.dart';
import 'package:shivish/apps/seller/presentation/cubits/performance/store_performance_state.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class StorePerformanceScreen extends StatelessWidget {
  const StorePerformanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Store Performance'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<StorePerformanceCubit>().refresh();
            },
          ),
        ],
      ),
      body: BlocBuilder<StorePerformanceCubit, StorePerformanceState>(
        builder: (context, state) {
          if (state.isLoading) {
            return const LoadingIndicator();
          }

          if (state.hasError) {
            return ErrorMessage(
              message:
                  state.errorMessage ?? 'Failed to load performance metrics',
              onRetry: () {
                context.read<StorePerformanceCubit>().refresh();
              },
            );
          }

          final metrics = state.metrics;
          if (metrics == null) {
            return const Center(
              child: Text('No performance metrics available'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTimeRangeSelector(context, state),
                const SizedBox(height: 24),
                _buildMetricsGrid(metrics),
                const SizedBox(height: 24),
                _buildPerformanceChart(metrics),
                const SizedBox(height: 24),
                _buildImprovementsList(metrics),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildTimeRangeSelector(
      BuildContext context, StorePerformanceState state) {
    return Row(
      children: [
        const Text('Time Range: '),
        const SizedBox(width: 8),
        DropdownButton<String>(
          value: state.timeRange,
          items: const [
            DropdownMenuItem(value: 'day', child: Text('Today')),
            DropdownMenuItem(value: 'week', child: Text('This Week')),
            DropdownMenuItem(value: 'month', child: Text('This Month')),
            DropdownMenuItem(value: 'year', child: Text('This Year')),
          ],
          onChanged: (value) {
            if (value != null) {
              context.read<StorePerformanceCubit>().setTimeRange(value);
            }
          },
        ),
      ],
    );
  }

  Widget _buildMetricsGrid(PerformanceMetrics metrics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'Conversion Rate',
          '${(metrics.conversionRate * 100).toStringAsFixed(1)}%',
          Icons.trending_up,
          Colors.green,
        ),
        _buildMetricCard(
          'Return Rate',
          '${(metrics.returnRate * 100).toStringAsFixed(1)}%',
          Icons.trending_down,
          Colors.red,
        ),
        _buildMetricCard(
          'Avg Processing Time',
          '${metrics.averageProcessingTime.toStringAsFixed(1)}h',
          Icons.timer,
          Colors.blue,
        ),
        _buildMetricCard(
          'Avg Delivery Time',
          '${metrics.averageDeliveryTime.toStringAsFixed(1)}d',
          Icons.local_shipping,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceChart(PerformanceMetrics metrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Trend',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: metrics.performanceByDay.length,
                itemBuilder: (context, index) {
                  final entry =
                      metrics.performanceByDay.entries.elementAt(index);
                  return Container(
                    width: 40,
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          entry.value.value.toStringAsFixed(1),
                          style: const TextStyle(fontSize: 12),
                        ),
                        const SizedBox(height: 4),
                        Container(
                          height: entry.value.value * 100,
                          decoration: BoxDecoration(
                            color: Colors.blue.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${entry.key.day}/${entry.key.month}',
                          style: const TextStyle(fontSize: 12),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImprovementsList(PerformanceMetrics metrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Suggested Improvements',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: metrics.improvements.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      const Icon(Icons.lightbulb_outline, color: Colors.amber),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(metrics.improvements[index]),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

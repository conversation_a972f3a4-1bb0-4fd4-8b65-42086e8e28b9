import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/order_cubit.dart';
import 'package:shivish/shared/models/order/order_model.dart';

class OrderFilters extends StatelessWidget {
  const OrderFilters({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderCubit, OrderState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const SizedBox(height: 16),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildStatusFilter(context, state.selectedStatus),
                  _buildDateRangeFilter(context, state),
                  if (state.selectedStatus != null || state.startDate != null)
                    _buildClearFilter(context),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatusFilter(BuildContext context, OrderStatus? selectedStatus) {
    return PopupMenuButton<OrderStatus>(
      initialValue: selectedStatus,
      onSelected: (status) {
        context.read<OrderCubit>().setStatusFilter(status);
      },
      itemBuilder: (context) => OrderStatus.values.map((status) {
        return PopupMenuItem(
          value: status,
          child: Row(
            children: [
              if (selectedStatus == status)
                const Icon(Icons.check, size: 18)
              else
                const SizedBox(width: 18),
              const SizedBox(width: 8),
              Text(status.name.toUpperCase()),
            ],
          ),
        );
      }).toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              selectedStatus?.name.toUpperCase() ?? 'All Orders',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 4),
            const Icon(Icons.arrow_drop_down, size: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeFilter(BuildContext context, OrderState state) {
    return InkWell(
      onTap: () async {
        final range = await showDateRangePicker(
          context: context,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          initialDateRange: DateTimeRange(
            start: state.startDate ??
                DateTime.now().subtract(const Duration(days: 30)),
            end: state.endDate ?? DateTime.now(),
          ),
        );

        if (range != null) {
          context.read<OrderCubit>().setDateRange(range.start, range.end);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              _formatDateRange(state.startDate, state.endDate),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 4),
            const Icon(Icons.calendar_today, size: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildClearFilter(BuildContext context) {
    return InkWell(
      onTap: () {
        context.read<OrderCubit>().clearFilters();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).dividerColor,
            width: 1,
          ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Clear Filters',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(width: 4),
            const Icon(Icons.clear, size: 16),
          ],
        ),
      ),
    );
  }

  String _formatDateRange(DateTime? startDate, DateTime? endDate) {
    if (startDate == null && endDate == null) {
      return 'All Time';
    }

    if (startDate == null) {
      return 'Until ${_formatDate(endDate!)}';
    }

    if (endDate == null) {
      return 'From ${_formatDate(startDate)}';
    }

    return '${_formatDate(startDate)} - ${_formatDate(endDate)}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

import express from 'express';
import 'express-async-errors';
declare class AuthServiceApp {
    app: express.Application;
    private databaseService;
    private redisService;
    constructor();
    private initializeMiddleware;
    private initializeRoutes;
    private initializeErrorHandling;
    initialize(): Promise<void>;
    start(): Promise<void>;
    shutdown(): Promise<void>;
}
export { AuthServiceApp };
//# sourceMappingURL=app.d.ts.map
interface Config {
    env: string;
    server: {
        port: number;
        host: string;
    };
    database: {
        host: string;
        port: number;
        name: string;
        user: string;
        password: string;
        ssl: boolean;
    };
    redis: {
        host: string;
        port: number;
        password?: string;
        db: number;
    };
    jwt: {
        accessTokenSecret: string;
        refreshTokenSecret: string;
        accessTokenExpiry: string;
        refreshTokenExpiry: string;
        issuer: string;
        audience: string;
    };
    security: {
        saltRounds: number;
        maxLoginAttempts: number;
        lockoutDuration: number;
        passwordMinLength: number;
        passwordRequireSpecialChar: boolean;
    };
    email: {
        host: string;
        port: number;
        secure: boolean;
        user: string;
        password: string;
        from: string;
    };
    cors: {
        allowedOrigins: string[];
    };
    social: {
        google: {
            clientId: string;
            clientSecret: string;
        };
        apple: {
            clientId: string;
            teamId: string;
            keyId: string;
            privateKey: string;
        };
    };
}
declare const config: Config;
export { config, Config };
//# sourceMappingURL=config.d.ts.map
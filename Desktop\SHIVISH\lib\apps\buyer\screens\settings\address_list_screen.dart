import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/models/user/user_model.dart';
import '../../../../shared/ui_components/errors/error_message.dart';
import '../../../../shared/ui_components/loading/loading_indicator.dart';
import '../../providers/settings/address_provider.dart';

class AddressListScreen extends ConsumerStatefulWidget {
  const AddressListScreen({super.key});

  @override
  ConsumerState<AddressListScreen> createState() => _AddressListScreenState();
}

class _AddressListScreenState extends ConsumerState<AddressListScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final addressesAsync = ref.watch(userAddressesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Addresses'),
      ),
      body: addressesAsync.when(
        loading: () => const LoadingIndicator(),
        error: (error, stackTrace) => ErrorMessage(
          message: error.toString(),
          onRetry: () => ref.refresh(userAddressesProvider),
        ),
        data: (addresses) => addresses.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 64,
                      color: theme.colorScheme.primary.withOpacity(0.5),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'No Addresses Added',
                      style: theme.textTheme.titleLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add your first address to get started',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 24),
                    FilledButton.icon(
                      onPressed: () => context.push('/settings/address/add'),
                      icon: const Icon(Icons.add),
                      label: const Text('Add New Address'),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: addresses.length,
                itemBuilder: (context, index) {
                  final address = addresses[index];
                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    child: InkWell(
                      onTap: () => context.push(
                        '/settings/address/edit',
                        extra: address,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  _getAddressTypeIcon(address),
                                  color: theme.colorScheme.primary,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _getAddressTypeLabel(address),
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.colorScheme.primary,
                                  ),
                                ),
                                if (address.isDefault) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8,
                                      vertical: 2,
                                    ),
                                    decoration: BoxDecoration(
                                      color: theme.colorScheme.primaryContainer,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Text(
                                      'Default',
                                      style:
                                          theme.textTheme.labelSmall?.copyWith(
                                        color: theme
                                            .colorScheme.onPrimaryContainer,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              address.contactName,
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              address.contactPhone,
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              address.street,
                              style: theme.textTheme.bodyMedium,
                            ),
                            if (address.landmark != null) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Near ${address.landmark}',
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                            const SizedBox(height: 4),
                            Text(
                              '${address.city}, ${address.state} ${address.postalCode}',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => context.push('/settings/address/add'),
        child: const Icon(Icons.add),
      ),
    );
  }

  IconData _getAddressTypeIcon(UserAddress address) {
    if (address.street.toLowerCase().contains('home')) {
      return Icons.home_outlined;
    } else if (address.street.toLowerCase().contains('office') ||
        address.street.toLowerCase().contains('work')) {
      return Icons.business_outlined;
    }
    return Icons.location_on_outlined;
  }

  String _getAddressTypeLabel(UserAddress address) {
    if (address.street.toLowerCase().contains('home')) {
      return 'Home';
    } else if (address.street.toLowerCase().contains('office') ||
        address.street.toLowerCase().contains('work')) {
      return 'Office';
    }
    return 'Other';
  }
}

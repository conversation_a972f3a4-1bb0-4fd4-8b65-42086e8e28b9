"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthServiceApp = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const compression_1 = __importDefault(require("compression"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
require("express-async-errors");
const config_1 = require("./config/config");
const logger_1 = require("./utils/logger");
const error_middleware_1 = require("./middleware/error.middleware");
const auth_routes_1 = require("./routes/auth.routes");
const user_routes_1 = require("./routes/user.routes");
const social_routes_1 = require("./routes/social.routes");
const database_service_1 = require("./services/database.service");
const redis_service_1 = require("./services/redis.service");
class AuthServiceApp {
    constructor() {
        this.app = (0, express_1.default)();
        this.databaseService = new database_service_1.DatabaseService();
        this.redisService = new redis_service_1.RedisService();
        this.initializeMiddleware();
        this.initializeRoutes();
        this.initializeErrorHandling();
    }
    initializeMiddleware() {
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        this.app.use((0, cors_1.default)({
            origin: config_1.config.cors.allowedOrigins,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
        }));
        this.app.use((0, compression_1.default)());
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
        this.app.use((0, cookie_parser_1.default)());
        const limiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: 100,
            message: {
                error: 'Too many requests from this IP, please try again later.',
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api/', limiter);
        const authLimiter = (0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: 5,
            message: {
                error: 'Too many authentication attempts, please try again later.',
            },
            standardHeaders: true,
            legacyHeaders: false,
        });
        this.app.use('/api/auth/login', authLimiter);
        this.app.use('/api/auth/register', authLimiter);
        this.app.use((req, res, next) => {
            logger_1.logger.info(`${req.method} ${req.path}`, {
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                timestamp: new Date().toISOString(),
            });
            next();
        });
    }
    initializeRoutes() {
        this.app.get('/health', (req, res) => {
            res.status(200).json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: process.env.npm_package_version || '1.0.0',
            });
        });
        this.app.use('/api/auth', auth_routes_1.authRoutes);
        this.app.use('/api/user', user_routes_1.userRoutes);
        this.app.use('/api/social', social_routes_1.socialRoutes);
        this.app.use('*', (req, res) => {
            res.status(404).json({
                error: 'Route not found',
                path: req.originalUrl,
            });
        });
    }
    initializeErrorHandling() {
        this.app.use(error_middleware_1.errorHandler);
    }
    async initialize() {
        try {
            await this.databaseService.connect();
            logger_1.logger.info('Database connected successfully');
            await this.redisService.connect();
            logger_1.logger.info('Redis connected successfully');
            await this.databaseService.runMigrations();
            logger_1.logger.info('Database migrations completed');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize services:', error);
            throw error;
        }
    }
    async start() {
        try {
            await this.initialize();
            const port = config_1.config.server.port;
            this.app.listen(port, () => {
                logger_1.logger.info(`Auth service started on port ${port}`);
                logger_1.logger.info(`Environment: ${config_1.config.env}`);
                logger_1.logger.info(`Health check: http://localhost:${port}/health`);
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to start auth service:', error);
            process.exit(1);
        }
    }
    async shutdown() {
        try {
            await this.databaseService.disconnect();
            await this.redisService.disconnect();
            logger_1.logger.info('Auth service shutdown completed');
        }
        catch (error) {
            logger_1.logger.error('Error during shutdown:', error);
        }
    }
}
exports.AuthServiceApp = AuthServiceApp;
const authService = new AuthServiceApp();
process.on('SIGTERM', async () => {
    logger_1.logger.info('SIGTERM received, shutting down gracefully');
    await authService.shutdown();
    process.exit(0);
});
process.on('SIGINT', async () => {
    logger_1.logger.info('SIGINT received, shutting down gracefully');
    await authService.shutdown();
    process.exit(0);
});
if (require.main === module) {
    authService.start().catch((error) => {
        logger_1.logger.error('Failed to start auth service:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=app.js.map
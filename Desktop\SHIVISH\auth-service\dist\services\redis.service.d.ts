export declare class RedisService {
    private client;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    set(key: string, value: string, ttlSeconds?: number): Promise<void>;
    get(key: string): Promise<string | null>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    setHash(key: string, field: string, value: string): Promise<void>;
    getHash(key: string, field: string): Promise<string | undefined>;
    getAllHash(key: string): Promise<Record<string, string>>;
    delHash(key: string, field: string): Promise<void>;
    increment(key: string): Promise<number>;
    expire(key: string, seconds: number): Promise<void>;
    healthCheck(): Promise<boolean>;
}
//# sourceMappingURL=redis.service.d.ts.map
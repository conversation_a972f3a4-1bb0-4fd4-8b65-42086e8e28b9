import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

part 'product_model.freezed.dart';
part 'product_model.g.dart';

enum ProductStatus {
  @JsonValue(0)
  draft,
  @JsonValue(1)
  pending,
  @JsonValue(2)
  approved,
  @JsonValue(3)
  rejected,
  @JsonValue(4)
  archived,
}

enum ProductType {
  @JsonValue(0)
  physical,
  @JsonValue(1)
  digital,
  @JsonValue(2)
  service,
}

@freezed
class ProductVariant with _$ProductVariant {
  const factory ProductVariant({
    required String id,
    required String name,
    required String sku,
    required double price,
    double? salePrice,
    double? compareAtPrice,
    required int quantity,
    String? barcode,
    String? imageUrl,
    Map<String, dynamic>? options,
    @Default(false) bool isDefault,
    @Default(true) bool isAvailable,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _ProductVariant;

  factory ProductVariant.fromJson(Map<String, dynamic> json) =>
      _$ProductVariantFromJson(json);

  factory ProductVariant.empty() => ProductVariant(
    id: '',
    name: '',
    sku: '',
    price: 0.0,
    quantity: 0,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

@freezed
class ProductCategory with _$ProductCategory {
  const factory ProductCategory({
    required String id,
    required String name,
    String? description,
    String? imageUrl,
    String? parentId,
    @Default(true) bool isActive,
    required DateTime createdAt,
    required DateTime updatedAt,
    @Default(false) bool isDeleted,
  }) = _ProductCategory;

  factory ProductCategory.fromJson(Map<String, dynamic> json) =>
      _$ProductCategoryFromJson(json);

  factory ProductCategory.empty() => ProductCategory(
    id: '',
    name: '',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
  );
}

/// Unified Product Model that serves as the single source of truth
@freezed
class ProductModel with _$ProductModel {
  const factory ProductModel({
    required String id,
    required String name,
    required String description,
    required double price,
    double? originalPrice,
    double? discountPercentage,
    double? salePrice,
    double? compareAtPrice,
    required int quantity,
    required String categoryId,
    required String sellerId,
    required List<String> images,
    @Default(false) bool isFeatured,
    @Default(false) bool isApproved,
    @Default(false) bool isDeleted,
    @Default(true) bool isActive,
    @Default(false) bool isSuspended,
    @Default([]) List<String> tags,
    String? brand,
    String? unit,
    double? weight,
    double? rating,
    int? reviewCount,
    @Default([]) List<String> highlights,
    Map<String, dynamic>? specifications,
    List<ProductVariant>? variants,
    @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
    DateTime? createdAt,
    @JsonKey(fromJson: _timestampFromJson, toJson: _timestampToJson)
    DateTime? updatedAt,
    required ProductType productType,
    required ProductStatus productStatus,
    String? verificationNotes,
    // Seller specific fields
    String? sellerName,
    String? sellerEmail,
    String? sellerPhone,
    String? sellerAddress,
    double? sellerRating,
    int? sellerTotalSales,
    int? sellerTotalProducts,
    bool? sellerIsVerified,
    bool? sellerIsActive,
    DateTime? sellerCreatedAt,
    DateTime? sellerUpdatedAt,
  }) = _ProductModel;

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);

  factory ProductModel.empty() => ProductModel(
    id: '',
    name: '',
    description: '',
    price: 0.0,
    originalPrice: null,
    discountPercentage: null,
    salePrice: null,
    compareAtPrice: null,
    quantity: 0,
    categoryId: '',
    sellerId: '',
    images: const [],
    isFeatured: false,
    isApproved: false,
    isDeleted: false,
    isActive: true,
    tags: const [],
    brand: null,
    unit: null,
    weight: null,
    rating: null,
    reviewCount: null,
    highlights: const [],
    specifications: null,
    variants: null,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    productType: ProductType.physical,
    productStatus: ProductStatus.draft,
    verificationNotes: null,
    // Seller specific fields
    sellerName: null,
    sellerEmail: null,
    sellerPhone: null,
    sellerAddress: null,
    sellerRating: null,
    sellerTotalSales: null,
    sellerTotalProducts: null,
    sellerIsVerified: null,
    sellerIsActive: null,
    sellerCreatedAt: null,
    sellerUpdatedAt: null,
  );
}

/// Converts a Firestore Timestamp to DateTime
DateTime? _timestampFromJson(dynamic value) {
  if (value == null) return null;
  if (value is Timestamp) return value.toDate();
  return null;
}

/// Converts a DateTime to Firestore Timestamp
dynamic _timestampToJson(DateTime? date) {
  if (date == null) return null;
  return Timestamp.fromDate(date);
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocialController = void 0;
const social_service_1 = require("../services/social.service");
const logger_1 = require("../utils/logger");
class SocialController {
    constructor() {
        this.googleLogin = async (req, res) => {
            try {
                const { accessToken, deviceInfo } = req.body;
                const result = await this.socialAuthService.googleLogin({
                    accessToken,
                    deviceInfo,
                    ipAddress: req.ip,
                });
                res.cookie('refreshToken', result.tokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'strict',
                    maxAge: 30 * 24 * 60 * 60 * 1000,
                });
                res.json({
                    success: true,
                    data: {
                        user: result.user,
                        accessToken: result.tokens.accessToken,
                        expiresAt: result.tokens.expiresAt,
                    },
                    message: 'Google login successful',
                });
            }
            catch (error) {
                logger_1.logger.error('Google login error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'GOOGLE_LOGIN_FAILED',
                        message: error.message || 'Google login failed',
                    },
                });
            }
        };
        this.appleLogin = async (req, res) => {
            try {
                const { accessToken, deviceInfo } = req.body;
                const result = await this.socialAuthService.appleLogin({
                    accessToken,
                    deviceInfo,
                    ipAddress: req.ip,
                });
                res.cookie('refreshToken', result.tokens.refreshToken, {
                    httpOnly: true,
                    secure: process.env.NODE_ENV === 'production',
                    sameSite: 'strict',
                    maxAge: 30 * 24 * 60 * 60 * 1000,
                });
                res.json({
                    success: true,
                    data: {
                        user: result.user,
                        accessToken: result.tokens.accessToken,
                        expiresAt: result.tokens.expiresAt,
                    },
                    message: 'Apple login successful',
                });
            }
            catch (error) {
                logger_1.logger.error('Apple login error:', error);
                res.status(error.statusCode || 500).json({
                    success: false,
                    error: {
                        code: error.code || 'APPLE_LOGIN_FAILED',
                        message: error.message || 'Apple login failed',
                    },
                });
            }
        };
        this.socialAuthService = new social_service_1.SocialAuthService();
    }
}
exports.SocialController = SocialController;
//# sourceMappingURL=social.controller.js.map
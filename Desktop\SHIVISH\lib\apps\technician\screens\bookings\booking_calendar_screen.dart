import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/apps/technician/providers/booking_provider.dart';
import 'package:shivish/apps/technician/providers/technician_provider.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';

class BookingCalendarScreen extends ConsumerStatefulWidget {
  const BookingCalendarScreen({super.key});

  @override
  ConsumerState<BookingCalendarScreen> createState() =>
      _BookingCalendarScreenState();
}

class _BookingCalendarScreenState extends ConsumerState<BookingCalendarScreen> {
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late CalendarFormat _calendarFormat;

  @override
  void initState() {
    super.initState();
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _calendarFormat = CalendarFormat.month;
  }

  @override
  Widget build(BuildContext context) {
    final technicianState = ref.watch(technicianProvider);

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'Booking Calendar',
        fallbackRoute: TechnicianRoutes.bookings,
      ),
      body: technicianState.when(
        data: (technician) {
          if (technician == null) {
            return const Center(
              child: Text('No technician data available'),
            );
          }

          return Column(
            children: [
              _buildCalendar(technician.id),
              const SizedBox(height: 16),
              _buildSelectedDayBookings(technician.id),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildCalendar(String technicianId) {
    return ref.watch(technicianBookingsProvider(technicianId)).when(
          data: (bookings) {
            final events = _getBookingEvents(bookings);

            return TableCalendar<BookingModel>(
              firstDay: DateTime.now().subtract(const Duration(days: 365)),
              lastDay: DateTime.now().add(const Duration(days: 365)),
              focusedDay: _focusedDay,
              selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
              calendarFormat: _calendarFormat,
              eventLoader: (day) => events[day] ?? [],
              startingDayOfWeek: StartingDayOfWeek.monday,
              calendarStyle: const CalendarStyle(
                markersMaxCount: 3,
                markerSize: 8,
                markerDecoration: BoxDecoration(
                  color: Colors.blue,
                  shape: BoxShape.circle,
                ),
              ),
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
              },
              onFormatChanged: (format) {
                setState(() {
                  _calendarFormat = format;
                });
              },
            );
          },
          loading: () => const Center(
            child: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => Center(
            child: Text('Error: $error'),
          ),
        );
  }

  Widget _buildSelectedDayBookings(String technicianId) {
    return ref.watch(technicianBookingsProvider(technicianId)).when(
          data: (bookings) {
            final selectedDayBookings = bookings
                .where(
                    (booking) => isSameDay(booking.bookingDate, _selectedDay))
                .toList();

            if (selectedDayBookings.isEmpty) {
              return const Expanded(
                child: Center(
                  child: Text('No bookings for selected day'),
                ),
              );
            }

            return Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.all(16),
                itemCount: selectedDayBookings.length,
                itemBuilder: (context, index) {
                  final booking = selectedDayBookings[index];
                  return _buildBookingCard(booking);
                },
              ),
            );
          },
          loading: () => const Expanded(
            child: Center(
              child: CircularProgressIndicator(),
            ),
          ),
          error: (error, stackTrace) => Expanded(
            child: Center(
              child: Text('Error: $error'),
            ),
          ),
        );
  }

  Widget _buildBookingCard(BookingModel booking) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(
          booking.services.isNotEmpty ? booking.services.first : 'Service',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${booking.startTime.hour}:${booking.startTime.minute.toString().padLeft(2, '0')} - ${booking.endTime.hour}:${booking.endTime.minute.toString().padLeft(2, '0')}',
            ),
            const SizedBox(height: 4),
            Text(booking.serviceLocation.contactName),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: _getStatusColor(booking.status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            booking.status.name.toUpperCase(),
            style: TextStyle(
              color: _getStatusColor(booking.status),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () {
          context.go('${TechnicianRoutes.bookingDetails}/${booking.id}');
        },
      ),
    );
  }

  Map<DateTime, List<BookingModel>> _getBookingEvents(
      List<BookingModel> bookings) {
    final events = <DateTime, List<BookingModel>>{};
    for (final booking in bookings) {
      final date = DateTime(
        booking.bookingDate.year,
        booking.bookingDate.month,
        booking.bookingDate.day,
      );
      events[date] = [...(events[date] ?? []), booking];
    }
    return events;
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return Colors.blue;
      case BookingStatus.inProgress:
        return Colors.purple;
      case BookingStatus.completed:
        return Colors.green;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.rescheduled:
        return Colors.amber;
      case BookingStatus.noShow:
        return Colors.grey;
      case BookingStatus.rejected:
        return Colors.redAccent;
    }
  }
}

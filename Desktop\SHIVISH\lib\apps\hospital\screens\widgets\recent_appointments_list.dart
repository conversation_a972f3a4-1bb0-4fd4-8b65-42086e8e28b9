import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:shivish/shared/models/hospital/appointment_model.dart';
import 'package:shivish/shared/providers/hospital/appointment_provider.dart';
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/ui_components/errors/error_message.dart';

class RecentAppointmentsList extends ConsumerWidget {
  const RecentAppointmentsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appointmentsAsync = ref.watch(recentAppointmentsProvider);

    return appointmentsAsync.when(
      data: (appointments) {
        if (appointments.isEmpty) {
          return const Center(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 24.0),
              child: Text(
                'No recent appointments found',
                style: TextStyle(
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          );
        }

        return ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: appointments.length,
          separatorBuilder: (context, index) => const Divider(),
          itemBuilder: (context, index) {
            final appointment = appointments[index];
            return _buildAppointmentItem(context, appointment);
          },
        );
      },
      loading: () => const Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 24.0),
          child: LoadingIndicator(size: 32),
        ),
      ),
      error: (error, stackTrace) => Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24.0),
          child: ErrorMessage(
            message: 'Failed to load appointments: $error',
            onRetry: () => ref.refresh(recentAppointmentsProvider),
          ),
        ),
      ),
    );
  }

  Widget _buildAppointmentItem(BuildContext context, AppointmentModel appointment) {
    return InkWell(
      onTap: () {
        context.push('/hospital/appointments/${appointment.id}');
      },
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            _getStatusIndicator(appointment.status),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    appointment.patientName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Dr. ${appointment.doctorName} • ${appointment.departmentName}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  DateFormat('MMM dd, yyyy').format(appointment.appointmentDate),
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${appointment.startTime} - ${appointment.endTime}',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _getStatusIndicator(AppointmentStatus status) {
    Color color;
    IconData icon;

    switch (status) {
      case AppointmentStatus.confirmed:
        color = Colors.blue;
        icon = Icons.check_circle;
        break;
      case AppointmentStatus.completed:
        color = Colors.green;
        icon = Icons.done_all;
        break;
      case AppointmentStatus.pending:
        color = Colors.orange;
        icon = Icons.access_time;
        break;
      case AppointmentStatus.cancelled:
        color = Colors.red;
        icon = Icons.cancel;
        break;
      case AppointmentStatus.rescheduled:
        color = Colors.purple;
        icon = Icons.event_repeat;
        break;
      case AppointmentStatus.noShow:
        color = Colors.grey;
        icon = Icons.person_off;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        color: color,
        size: 20,
      ),
    );
  }


}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserMapper = exports.SocialProvider = exports.UserStatus = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["BUYER"] = "buyer";
    UserRole["SELLER"] = "seller";
    UserRole["PRIEST"] = "priest";
    UserRole["TECHNICIAN"] = "technician";
    UserRole["EXECUTOR"] = "executor";
    UserRole["ADMIN"] = "admin";
})(UserRole || (exports.UserRole = UserRole = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "active";
    UserStatus["INACTIVE"] = "inactive";
    UserStatus["SUSPENDED"] = "suspended";
    UserStatus["PENDING_VERIFICATION"] = "pending_verification";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var SocialProvider;
(function (SocialProvider) {
    SocialProvider["GOOGLE"] = "google";
    SocialProvider["APPLE"] = "apple";
    SocialProvider["FACEBOOK"] = "facebook";
})(SocialProvider || (exports.SocialProvider = SocialProvider = {}));
class UserMapper {
    static fromRow(row) {
        return {
            id: row.id,
            email: row.email,
            passwordHash: row.password_hash,
            displayName: row.display_name,
            phoneNumber: row.phone_number,
            photoUrl: row.photo_url,
            role: row.role,
            status: row.status,
            emailVerified: row.email_verified,
            phoneVerified: row.phone_verified,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            lastLoginAt: row.last_login_at,
            failedLoginAttempts: row.failed_login_attempts,
            lockedUntil: row.locked_until,
        };
    }
    static toProfile(user) {
        return {
            id: user.id,
            email: user.email,
            displayName: user.displayName,
            phoneNumber: user.phoneNumber,
            photoUrl: user.photoUrl,
            role: user.role,
            status: user.status,
            emailVerified: user.emailVerified,
            phoneVerified: user.phoneVerified,
            createdAt: user.createdAt,
            lastLoginAt: user.lastLoginAt,
        };
    }
    static sessionFromRow(row) {
        return {
            id: row.id,
            userId: row.user_id,
            refreshToken: row.refresh_token,
            deviceInfo: row.device_info,
            ipAddress: row.ip_address,
            expiresAt: row.expires_at,
            createdAt: row.created_at,
            lastUsedAt: row.last_used_at,
        };
    }
    static socialAccountFromRow(row) {
        return {
            id: row.id,
            userId: row.user_id,
            provider: row.provider,
            providerId: row.provider_id,
            providerData: row.provider_data,
            createdAt: row.created_at,
        };
    }
    static passwordResetTokenFromRow(row) {
        return {
            id: row.id,
            userId: row.user_id,
            token: row.token,
            expiresAt: row.expires_at,
            used: row.used,
            createdAt: row.created_at,
        };
    }
}
exports.UserMapper = UserMapper;
//# sourceMappingURL=user.model.js.map
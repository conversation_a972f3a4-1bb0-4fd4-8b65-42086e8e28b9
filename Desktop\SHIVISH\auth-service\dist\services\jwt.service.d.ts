export interface AccessTokenPayload {
    sub: string;
    email: string;
    role: string;
    iat: number;
    exp: number;
    iss: string;
    aud: string;
}
export interface RefreshTokenPayload {
    sub: string;
    jti: string;
    iat: number;
    exp: number;
    iss: string;
    aud: string;
}
export interface TokenPair {
    accessToken: string;
    refreshToken: string;
    accessTokenExpiry: Date;
    refreshTokenExpiry: Date;
}
export declare class JWTService {
    private readonly accessTokenSecret;
    private readonly refreshTokenSecret;
    private readonly issuer;
    private readonly audience;
    constructor();
    generateTokenPair(userId: string, email: string, role: string): TokenPair;
    verifyAccessToken(token: string): AccessTokenPayload;
    verifyRefreshToken(token: string): RefreshTokenPayload;
    refreshAccessToken(refreshToken: string, email: string, role: string): string;
    extractTokenFromHeader(authHeader: string | undefined): string | null;
    getTokenExpiry(token: string): Date | null;
    isTokenExpired(token: string): boolean;
    private parseExpiry;
}
export declare const jwtService: JWTService;
//# sourceMappingURL=jwt.service.d.ts.map
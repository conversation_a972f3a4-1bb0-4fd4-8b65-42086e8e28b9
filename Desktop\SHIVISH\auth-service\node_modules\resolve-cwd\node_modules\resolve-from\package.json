{"name": "resolve-from", "version": "5.0.0", "description": "Resolve the path of a module like `require.resolve()` but from a given path", "license": "MIT", "repository": "sindresorhus/resolve-from", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["require", "resolve", "path", "module", "from", "like", "import"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import '../../providers/shopping/shopping_list_provider.dart';
import '../../../../shared/models/shopping/shopping_list_model.dart';
import '../../../../shared/providers/auth_provider.dart';

/// Screen to display and manage shopping list templates
class ShoppingListTemplatesScreen extends ConsumerStatefulWidget {
  /// Creates a [ShoppingListTemplatesScreen]
  const ShoppingListTemplatesScreen({super.key});

  @override
  ConsumerState<ShoppingListTemplatesScreen> createState() =>
      _ShoppingListTemplatesScreenState();
}

class _ShoppingListTemplatesScreenState
    extends ConsumerState<ShoppingListTemplatesScreen> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = ref.watch(currentUserProvider).value;

    final templatesAsync = ref.watch(userTemplatesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Templates'),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: templatesAsync.when(
        data: (templates) {
          if (templates.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 64,
                    color: theme.colorScheme.primary.withAlpha(128),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'No Templates',
                    style: theme.textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Save your shopping lists as templates for reuse',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: templates.length,
            itemBuilder: (context, index) {
              final template = templates[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: ListTile(
                  title: Text(template.name),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (template.description.isNotEmpty)
                        Text(
                          template.description,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      const SizedBox(height: 4),
                      Text(
                        '${template.itemCount} items',
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.bookmark,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                  trailing: PopupMenuButton<String>(
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'view',
                        child: Text('View Items'),
                      ),
                      const PopupMenuItem(
                        value: 'use',
                        child: Text('Use Template'),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Text('Delete Template'),
                      ),
                    ],
                    onSelected: (value) async {
                      if (value == 'view') {
                        // Show template items
                        if (mounted) {
                          showDialog<void>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(template.name),
                              content: SizedBox(
                                width: double.maxFinite,
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: template.items.length,
                                  itemBuilder: (context, index) {
                                    final item = template.items[index];
                                    return ListTile(
                                      title: Text(item.name),
                                      subtitle: Text('Qty: ${item.quantity}'),
                                    );
                                  },
                                ),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.of(context).pop(),
                                  child: const Text('CLOSE'),
                                ),
                              ],
                            ),
                          );
                        }
                      } else if (value == 'use') {
                        // Create a new list from template
                        try {
                          final now = DateTime.now();
                          final newList = ShoppingListModel(
                            id: const Uuid().v4(),
                            name: template.name.replaceAll(' (Template)', ''),
                            description: template.description,
                            itemCount: template.itemCount,
                            totalPrice: 0, // Reset price for new list
                            isShared: false,
                            isTemplate: false,
                            createdAt: now,
                            updatedAt: now,
                            createdBy: user?.uid ?? '',
                            items: template.items,
                            status: 'pending',
                          );

                          await ref
                              .read(shoppingListServiceProvider)
                              .createShoppingList(newList);

                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('List created from template'),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        } catch (e) {
                          if (mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Failed to create list: $e'),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      } else if (value == 'delete') {
                        // Delete template
                        final confirmed = await showDialog<bool>(
                              context: context,
                              builder: (context) => AlertDialog(
                                title: const Text('Delete Template'),
                                content: Text(
                                  'Are you sure you want to delete "${template.name}"?',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(false),
                                    child: const Text('CANCEL'),
                                  ),
                                  FilledButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(true),
                                    style: FilledButton.styleFrom(
                                      backgroundColor: Colors.red,
                                    ),
                                    child: const Text('DELETE'),
                                  ),
                                ],
                              ),
                            ) ??
                            false;

                        if (confirmed) {
                          try {
                            await ref
                                .read(shoppingListServiceProvider)
                                .deleteShoppingList(template.id);

                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Template deleted'),
                                  backgroundColor: Colors.green,
                                ),
                              );
                            }
                          } catch (e) {
                            if (mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content:
                                      Text('Failed to delete template: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            }
                          }
                        }
                      }
                    },
                  ),
                ),
              );
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error loading templates: $error'),
        ),
      ),
    );
  }
}

import { PoolClient, QueryResult } from 'pg';
export declare class DatabaseService {
    private pool;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    query<T = any>(text: string, params?: any[]): Promise<QueryResult<T>>;
    transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T>;
    runMigrations(): Promise<void>;
    healthCheck(): Promise<boolean>;
}
export declare const databaseService: DatabaseService;
//# sourceMappingURL=database.service.d.ts.map
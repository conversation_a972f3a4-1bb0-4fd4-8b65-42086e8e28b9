import 'package:flutter/material.dart';
import '../../../../shared/models/delivery/delivery_person_model.dart';

class StatusToggleCard extends StatelessWidget {
  final DeliveryPersonStatus currentStatus;
  final Function(DeliveryPersonStatus) onStatusChanged;

  const StatusToggleCard({
    super.key,
    required this.currentStatus,
    required this.onStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Your Status',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Status toggle buttons
            Row(
              children: [
                Expanded(
                  child: _buildStatusButton(
                    context,
                    status: DeliveryPersonStatus.available,
                    icon: Icons.check_circle,
                    label: 'Available',
                    color: Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusButton(
                    context,
                    status: DeliveryPersonStatus.onBreak,
                    icon: Icons.coffee,
                    label: 'On Break',
                    color: Colors.orange,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatusButton(
                    context,
                    status: DeliveryPersonStatus.offline,
                    icon: Icons.power_settings_new,
                    label: 'Offline',
                    color: Colors.grey,
                  ),
                ),
              ],
            ),

            // Status description
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getStatusColor(currentStatus).withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getStatusColor(currentStatus).withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _getStatusIcon(currentStatus),
                    color: _getStatusColor(currentStatus),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'You are ${_getStatusText(currentStatus)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getStatusColor(currentStatus),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getStatusDescription(currentStatus),
                          style: theme.textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusButton(
    BuildContext context, {
    required DeliveryPersonStatus status,
    required IconData icon,
    required String label,
    required Color color,
  }) {
    final isSelected = currentStatus == status;

    // Don't allow changing to delivering status directly
    if (status == DeliveryPersonStatus.delivering) {
      return const SizedBox.shrink();
    }

    // Don't allow changing status if currently delivering
    if (currentStatus == DeliveryPersonStatus.delivering &&
        status != DeliveryPersonStatus.delivering) {
      return Opacity(
        opacity: 0.5,
        child: _buildButtonContent(
          context,
          icon: icon,
          label: label,
          color: color,
          isSelected: false,
        ),
      );
    }

    return InkWell(
      onTap: () {
        if (currentStatus != status) {
          onStatusChanged(status);
        }
      },
      borderRadius: BorderRadius.circular(8),
      child: _buildButtonContent(
        context,
        icon: icon,
        label: label,
        color: color,
        isSelected: isSelected,
      ),
    );
  }

  Widget _buildButtonContent(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required bool isSelected,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: isSelected ? color.withOpacity(0.1) : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? color : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            color: isSelected ? color : Colors.grey,
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: isSelected ? color : Colors.grey[600],
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText(DeliveryPersonStatus status) {
    switch (status) {
      case DeliveryPersonStatus.available:
        return 'Available';
      case DeliveryPersonStatus.delivering:
        return 'Delivering';
      case DeliveryPersonStatus.offline:
        return 'Offline';
      case DeliveryPersonStatus.onBreak:
        return 'On Break';
    }
  }

  String _getStatusDescription(DeliveryPersonStatus status) {
    switch (status) {
      case DeliveryPersonStatus.available:
        return 'You are available to receive new delivery requests.';
      case DeliveryPersonStatus.delivering:
        return 'You are currently delivering an order. Complete it to become available again.';
      case DeliveryPersonStatus.offline:
        return 'You are offline and will not receive any delivery requests.';
      case DeliveryPersonStatus.onBreak:
        return 'You are on a break and will not receive any delivery requests.';
    }
  }

  IconData _getStatusIcon(DeliveryPersonStatus status) {
    switch (status) {
      case DeliveryPersonStatus.available:
        return Icons.check_circle;
      case DeliveryPersonStatus.delivering:
        return Icons.delivery_dining;
      case DeliveryPersonStatus.offline:
        return Icons.power_settings_new;
      case DeliveryPersonStatus.onBreak:
        return Icons.coffee;
    }
  }

  Color _getStatusColor(DeliveryPersonStatus status) {
    switch (status) {
      case DeliveryPersonStatus.available:
        return Colors.green;
      case DeliveryPersonStatus.delivering:
        return Colors.blue;
      case DeliveryPersonStatus.offline:
        return Colors.grey;
      case DeliveryPersonStatus.onBreak:
        return Colors.orange;
    }
  }
}

export declare class PasswordService {
    private readonly saltLength;
    private readonly hashOptions;
    hashPassword(password: string): Promise<string>;
    verifyPassword(password: string, hash: string): Promise<boolean>;
    validatePassword(password: string): {
        isValid: boolean;
        errors: string[];
    };
    generateSecurePassword(length?: number): string;
    generateResetToken(): string;
    generateVerificationToken(): string;
    private hasCommonPatterns;
    private getRandomChar;
    calculatePasswordStrength(password: string): number;
    getPasswordStrengthDescription(score: number): string;
}
export declare const passwordService: PasswordService;
//# sourceMappingURL=password.service.d.ts.map
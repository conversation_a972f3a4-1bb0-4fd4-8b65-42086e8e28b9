"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SocialAuthService = void 0;
const jwt_service_1 = require("./jwt.service");
const database_service_1 = require("./database.service");
const logger_1 = require("../utils/logger");
const user_model_1 = require("../models/user.model");
class SocialAuthService {
    async googleLogin(request) {
        try {
            const googleUserData = await this.verifyGoogleToken(request.accessToken);
            let user = await this.findUserByEmail(googleUserData.email);
            if (!user) {
                user = await this.createUserFromSocial({
                    email: googleUserData.email,
                    displayName: googleUserData.name,
                    photoUrl: googleUserData.picture,
                    provider: 'google',
                    providerId: googleUserData.id,
                    providerData: googleUserData,
                });
            }
            else {
                await this.linkSocialAccount(user.id, {
                    provider: 'google',
                    providerId: googleUserData.id,
                    providerData: googleUserData,
                });
            }
            const tokens = jwt_service_1.jwtService.generateTokenPair(user.id, user.email, user.role);
            await database_service_1.databaseService.query(`INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
         VALUES ($1, $2, $3, $4, $5)`, [
                user.id,
                tokens.refreshToken,
                request.deviceInfo ? JSON.stringify(request.deviceInfo) : null,
                request.ipAddress,
                tokens.refreshTokenExpiry
            ]);
            return {
                user: user_model_1.UserMapper.toProfile(user),
                tokens: {
                    accessToken: tokens.accessToken,
                    refreshToken: tokens.refreshToken,
                    expiresAt: tokens.accessTokenExpiry,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Google login failed:', error);
            throw error;
        }
    }
    async appleLogin(request) {
        try {
            const appleUserData = await this.verifyAppleToken(request.accessToken);
            let user = await this.findUserByEmail(appleUserData.email);
            if (!user) {
                user = await this.createUserFromSocial({
                    email: appleUserData.email,
                    displayName: appleUserData.name,
                    provider: 'apple',
                    providerId: appleUserData.id,
                    providerData: appleUserData,
                });
            }
            else {
                await this.linkSocialAccount(user.id, {
                    provider: 'apple',
                    providerId: appleUserData.id,
                    providerData: appleUserData,
                });
            }
            const tokens = jwt_service_1.jwtService.generateTokenPair(user.id, user.email, user.role);
            await database_service_1.databaseService.query(`INSERT INTO user_sessions (user_id, refresh_token, device_info, ip_address, expires_at)
         VALUES ($1, $2, $3, $4, $5)`, [
                user.id,
                tokens.refreshToken,
                request.deviceInfo ? JSON.stringify(request.deviceInfo) : null,
                request.ipAddress,
                tokens.refreshTokenExpiry
            ]);
            return {
                user: user_model_1.UserMapper.toProfile(user),
                tokens: {
                    accessToken: tokens.accessToken,
                    refreshToken: tokens.refreshToken,
                    expiresAt: tokens.accessTokenExpiry,
                },
            };
        }
        catch (error) {
            logger_1.logger.error('Apple login failed:', error);
            throw error;
        }
    }
    async verifyGoogleToken(accessToken) {
        logger_1.logger.info('Verifying Google token:', accessToken);
        return {
            id: 'google_user_123',
            email: '<EMAIL>',
            name: 'Google User',
            picture: 'https://example.com/avatar.jpg',
        };
    }
    async verifyAppleToken(accessToken) {
        logger_1.logger.info('Verifying Apple token:', accessToken);
        return {
            id: 'apple_user_123',
            email: '<EMAIL>',
            name: 'Apple User',
        };
    }
    async findUserByEmail(email) {
        const result = await database_service_1.databaseService.query('SELECT * FROM users WHERE email = $1', [email]);
        if (result.rows.length === 0) {
            return null;
        }
        return user_model_1.UserMapper.fromRow(result.rows[0]);
    }
    async createUserFromSocial(data) {
        return await database_service_1.databaseService.transaction(async (client) => {
            const userResult = await client.query(`INSERT INTO users (email, password_hash, display_name, photo_url, role, status, email_verified)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING *`, [
                data.email,
                '',
                data.displayName || null,
                data.photoUrl || null,
                'buyer',
                'active',
                true,
            ]);
            const user = user_model_1.UserMapper.fromRow(userResult.rows[0]);
            await client.query(`INSERT INTO social_accounts (user_id, provider, provider_id, provider_data)
         VALUES ($1, $2, $3, $4)`, [user.id, data.provider, data.providerId, JSON.stringify(data.providerData)]);
            return user;
        });
    }
    async linkSocialAccount(userId, data) {
        const existingResult = await database_service_1.databaseService.query('SELECT * FROM social_accounts WHERE user_id = $1 AND provider = $2', [userId, data.provider]);
        if (existingResult.rows.length === 0) {
            await database_service_1.databaseService.query(`INSERT INTO social_accounts (user_id, provider, provider_id, provider_data)
         VALUES ($1, $2, $3, $4)`, [userId, data.provider, data.providerId, JSON.stringify(data.providerData)]);
        }
    }
}
exports.SocialAuthService = SocialAuthService;
//# sourceMappingURL=social.service.js.map
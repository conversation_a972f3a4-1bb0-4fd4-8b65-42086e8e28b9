export interface User {
    id: string;
    email: string;
    passwordHash: string;
    displayName?: string;
    phoneNumber?: string;
    photoUrl?: string;
    role: UserRole;
    status: UserStatus;
    emailVerified: boolean;
    phoneVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
    lastLoginAt?: Date;
    failedLoginAttempts: number;
    lockedUntil?: Date;
}
export interface CreateUserRequest {
    email: string;
    password: string;
    displayName?: string;
    phoneNumber?: string;
    role?: UserRole;
}
export interface UpdateUserRequest {
    displayName?: string;
    phoneNumber?: string;
    photoUrl?: string;
}
export interface UserSession {
    id: string;
    userId: string;
    refreshToken: string;
    deviceInfo?: DeviceInfo;
    ipAddress?: string;
    expiresAt: Date;
    createdAt: Date;
    lastUsedAt: Date;
}
export interface DeviceInfo {
    platform?: string;
    deviceId?: string;
    appVersion?: string;
    osVersion?: string;
    deviceModel?: string;
    userAgent?: string;
}
export interface SocialAccount {
    id: string;
    userId: string;
    provider: SocialProvider;
    providerId: string;
    providerData: Record<string, any>;
    createdAt: Date;
}
export interface PasswordResetToken {
    id: string;
    userId: string;
    token: string;
    expiresAt: Date;
    used: boolean;
    createdAt: Date;
}
export declare enum UserRole {
    BUYER = "buyer",
    SELLER = "seller",
    PRIEST = "priest",
    TECHNICIAN = "technician",
    EXECUTOR = "executor",
    ADMIN = "admin"
}
export declare enum UserStatus {
    ACTIVE = "active",
    INACTIVE = "inactive",
    SUSPENDED = "suspended",
    PENDING_VERIFICATION = "pending_verification"
}
export declare enum SocialProvider {
    GOOGLE = "google",
    APPLE = "apple",
    FACEBOOK = "facebook"
}
export interface UserProfile {
    id: string;
    email: string;
    displayName?: string;
    phoneNumber?: string;
    photoUrl?: string;
    role: UserRole;
    status: UserStatus;
    emailVerified: boolean;
    phoneVerified: boolean;
    createdAt: Date;
    lastLoginAt?: Date;
}
export interface LoginRequest {
    email: string;
    password: string;
    deviceInfo?: DeviceInfo;
}
export interface RegisterRequest {
    email: string;
    password: string;
    displayName?: string;
    phoneNumber?: string;
    role?: UserRole;
    deviceInfo?: DeviceInfo;
}
export interface AuthResponse {
    user: UserProfile;
    accessToken: string;
    refreshToken: string;
    expiresAt: Date;
}
export interface RefreshTokenRequest {
    refreshToken: string;
    deviceInfo?: DeviceInfo;
}
export interface ForgotPasswordRequest {
    email: string;
}
export interface ResetPasswordRequest {
    token: string;
    newPassword: string;
}
export interface ChangePasswordRequest {
    currentPassword: string;
    newPassword: string;
}
export interface VerifyEmailRequest {
    token: string;
}
export interface SocialLoginRequest {
    provider: SocialProvider;
    accessToken: string;
    deviceInfo?: DeviceInfo;
}
export interface UserRow {
    id: string;
    email: string;
    password_hash: string;
    display_name?: string;
    phone_number?: string;
    photo_url?: string;
    role: string;
    status: string;
    email_verified: boolean;
    phone_verified: boolean;
    created_at: Date;
    updated_at: Date;
    last_login_at?: Date;
    failed_login_attempts: number;
    locked_until?: Date;
}
export interface UserSessionRow {
    id: string;
    user_id: string;
    refresh_token: string;
    device_info?: any;
    ip_address?: string;
    expires_at: Date;
    created_at: Date;
    last_used_at: Date;
}
export interface SocialAccountRow {
    id: string;
    user_id: string;
    provider: string;
    provider_id: string;
    provider_data: any;
    created_at: Date;
}
export interface PasswordResetTokenRow {
    id: string;
    user_id: string;
    token: string;
    expires_at: Date;
    used: boolean;
    created_at: Date;
}
export declare class UserMapper {
    static fromRow(row: UserRow): User;
    static toProfile(user: User): UserProfile;
    static sessionFromRow(row: UserSessionRow): UserSession;
    static socialAccountFromRow(row: SocialAccountRow): SocialAccount;
    static passwordResetTokenFromRow(row: PasswordResetTokenRow): PasswordResetToken;
}
//# sourceMappingURL=user.model.d.ts.map
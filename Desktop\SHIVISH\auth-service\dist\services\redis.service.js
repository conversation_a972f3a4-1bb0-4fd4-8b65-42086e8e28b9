"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const redis_1 = require("redis");
const config_1 = require("../config/config");
const logger_1 = require("../utils/logger");
class RedisService {
    constructor() {
        this.client = null;
        this.client = (0, redis_1.createClient)({
            socket: {
                host: config_1.config.redis.host,
                port: config_1.config.redis.port,
            },
            password: config_1.config.redis.password,
            database: config_1.config.redis.db,
        });
        this.client.on('error', (err) => {
            logger_1.logger.error('Redis Client Error:', err);
        });
        this.client.on('connect', () => {
            logger_1.logger.info('Redis client connected');
        });
        this.client.on('ready', () => {
            logger_1.logger.info('Redis client ready');
        });
        this.client.on('end', () => {
            logger_1.logger.info('Redis client disconnected');
        });
    }
    async connect() {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            await this.client.connect();
            logger_1.logger.info('Redis connected successfully');
        }
        catch (error) {
            logger_1.logger.error('Redis connection failed:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.client && this.client.isOpen) {
                await this.client.disconnect();
                logger_1.logger.info('Redis disconnected successfully');
            }
        }
        catch (error) {
            logger_1.logger.error('Redis disconnection failed:', error);
            throw error;
        }
    }
    async set(key, value, ttlSeconds) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            if (ttlSeconds) {
                await this.client.setEx(key, ttlSeconds, value);
            }
            else {
                await this.client.set(key, value);
            }
        }
        catch (error) {
            logger_1.logger.error('Redis SET error:', error);
            throw error;
        }
    }
    async get(key) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            return await this.client.get(key);
        }
        catch (error) {
            logger_1.logger.error('Redis GET error:', error);
            throw error;
        }
    }
    async del(key) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            await this.client.del(key);
        }
        catch (error) {
            logger_1.logger.error('Redis DEL error:', error);
            throw error;
        }
    }
    async exists(key) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            const result = await this.client.exists(key);
            return result === 1;
        }
        catch (error) {
            logger_1.logger.error('Redis EXISTS error:', error);
            throw error;
        }
    }
    async setHash(key, field, value) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            await this.client.hSet(key, field, value);
        }
        catch (error) {
            logger_1.logger.error('Redis HSET error:', error);
            throw error;
        }
    }
    async getHash(key, field) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            return await this.client.hGet(key, field);
        }
        catch (error) {
            logger_1.logger.error('Redis HGET error:', error);
            throw error;
        }
    }
    async getAllHash(key) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            return await this.client.hGetAll(key);
        }
        catch (error) {
            logger_1.logger.error('Redis HGETALL error:', error);
            throw error;
        }
    }
    async delHash(key, field) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            await this.client.hDel(key, field);
        }
        catch (error) {
            logger_1.logger.error('Redis HDEL error:', error);
            throw error;
        }
    }
    async increment(key) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            return await this.client.incr(key);
        }
        catch (error) {
            logger_1.logger.error('Redis INCR error:', error);
            throw error;
        }
    }
    async expire(key, seconds) {
        try {
            if (!this.client) {
                throw new Error('Redis client not initialized');
            }
            await this.client.expire(key, seconds);
        }
        catch (error) {
            logger_1.logger.error('Redis EXPIRE error:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            if (!this.client) {
                return false;
            }
            await this.client.ping();
            return true;
        }
        catch (error) {
            logger_1.logger.error('Redis health check failed:', error);
            return false;
        }
    }
}
exports.RedisService = RedisService;
//# sourceMappingURL=redis.service.js.map
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseService = exports.DatabaseService = void 0;
const pg_1 = require("pg");
const config_1 = require("../config/config");
const logger_1 = require("../utils/logger");
class DatabaseService {
    constructor() {
        this.pool = null;
        this.pool = new pg_1.Pool({
            host: config_1.config.database.host,
            port: config_1.config.database.port,
            database: config_1.config.database.name,
            user: config_1.config.database.user,
            password: config_1.config.database.password,
            ssl: config_1.config.database.ssl ? { rejectUnauthorized: false } : false,
            max: 20,
            idleTimeoutMillis: 30000,
            connectionTimeoutMillis: 2000,
        });
        this.pool.on('error', (err) => {
            logger_1.logger.error('Unexpected error on idle client', err);
        });
    }
    async connect() {
        try {
            if (!this.pool) {
                throw new Error('Database pool not initialized');
            }
            const client = await this.pool.connect();
            await client.query('SELECT NOW()');
            client.release();
            logger_1.logger.info('Database connected successfully');
        }
        catch (error) {
            logger_1.logger.error('Database connection failed:', error);
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.pool) {
                await this.pool.end();
                this.pool = null;
                logger_1.logger.info('Database disconnected successfully');
            }
        }
        catch (error) {
            logger_1.logger.error('Database disconnection failed:', error);
            throw error;
        }
    }
    async query(text, params) {
        if (!this.pool) {
            throw new Error('Database pool not initialized');
        }
        const start = Date.now();
        try {
            const result = await this.pool.query(text, params);
            const duration = Date.now() - start;
            logger_1.logger.debug('Query executed', {
                text: text.substring(0, 100),
                duration: `${duration}ms`,
                rows: result.rowCount,
            });
            return result;
        }
        catch (error) {
            logger_1.logger.error('Query execution failed:', {
                text: text.substring(0, 100),
                params,
                error,
            });
            throw error;
        }
    }
    async transaction(callback) {
        if (!this.pool) {
            throw new Error('Database pool not initialized');
        }
        const client = await this.pool.connect();
        try {
            await client.query('BEGIN');
            const result = await callback(client);
            await client.query('COMMIT');
            return result;
        }
        catch (error) {
            await client.query('ROLLBACK');
            throw error;
        }
        finally {
            client.release();
        }
    }
    async runMigrations() {
        try {
            logger_1.logger.info('Running database migrations...');
            await this.query(`
        CREATE TABLE IF NOT EXISTS migrations (
          id SERIAL PRIMARY KEY,
          name VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP DEFAULT NOW()
        )
      `);
            const migrations = [
                {
                    name: '001_create_users_table',
                    sql: `
            CREATE TABLE IF NOT EXISTS users (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              email VARCHAR(255) UNIQUE NOT NULL,
              password_hash VARCHAR(255) NOT NULL,
              display_name VARCHAR(255),
              phone_number VARCHAR(20),
              photo_url TEXT,
              role VARCHAR(50) NOT NULL DEFAULT 'buyer',
              status VARCHAR(50) NOT NULL DEFAULT 'active',
              email_verified BOOLEAN DEFAULT FALSE,
              phone_verified BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMP DEFAULT NOW(),
              updated_at TIMESTAMP DEFAULT NOW(),
              last_login_at TIMESTAMP,
              failed_login_attempts INTEGER DEFAULT 0,
              locked_until TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
            CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
            CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
          `,
                },
                {
                    name: '002_create_sessions_table',
                    sql: `
            CREATE TABLE IF NOT EXISTS user_sessions (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              user_id UUID REFERENCES users(id) ON DELETE CASCADE,
              refresh_token VARCHAR(255) UNIQUE NOT NULL,
              device_info JSONB,
              ip_address INET,
              expires_at TIMESTAMP NOT NULL,
              created_at TIMESTAMP DEFAULT NOW(),
              last_used_at TIMESTAMP DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_sessions(user_id);
            CREATE INDEX IF NOT EXISTS idx_sessions_refresh_token ON user_sessions(refresh_token);
            CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_sessions(expires_at);
          `,
                },
                {
                    name: '003_create_social_accounts_table',
                    sql: `
            CREATE TABLE IF NOT EXISTS social_accounts (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              user_id UUID REFERENCES users(id) ON DELETE CASCADE,
              provider VARCHAR(50) NOT NULL,
              provider_id VARCHAR(255) NOT NULL,
              provider_data JSONB,
              created_at TIMESTAMP DEFAULT NOW(),
              UNIQUE(provider, provider_id)
            );
            
            CREATE INDEX IF NOT EXISTS idx_social_accounts_user_id ON social_accounts(user_id);
            CREATE INDEX IF NOT EXISTS idx_social_accounts_provider ON social_accounts(provider, provider_id);
          `,
                },
                {
                    name: '004_create_password_reset_tokens_table',
                    sql: `
            CREATE TABLE IF NOT EXISTS password_reset_tokens (
              id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
              user_id UUID REFERENCES users(id) ON DELETE CASCADE,
              token VARCHAR(255) UNIQUE NOT NULL,
              expires_at TIMESTAMP NOT NULL,
              used BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMP DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON password_reset_tokens(user_id);
            CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token ON password_reset_tokens(token);
            CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at);
          `,
                },
            ];
            for (const migration of migrations) {
                const result = await this.query('SELECT name FROM migrations WHERE name = $1', [migration.name]);
                if (result.rows.length === 0) {
                    logger_1.logger.info(`Running migration: ${migration.name}`);
                    await this.query(migration.sql);
                    await this.query('INSERT INTO migrations (name) VALUES ($1)', [migration.name]);
                    logger_1.logger.info(`Migration completed: ${migration.name}`);
                }
                else {
                    logger_1.logger.debug(`Migration already executed: ${migration.name}`);
                }
            }
            logger_1.logger.info('Database migrations completed successfully');
        }
        catch (error) {
            logger_1.logger.error('Database migrations failed:', error);
            throw error;
        }
    }
    async healthCheck() {
        try {
            await this.query('SELECT 1');
            return true;
        }
        catch (error) {
            logger_1.logger.error('Database health check failed:', error);
            return false;
        }
    }
}
exports.DatabaseService = DatabaseService;
exports.databaseService = new DatabaseService();
//# sourceMappingURL=database.service.js.map
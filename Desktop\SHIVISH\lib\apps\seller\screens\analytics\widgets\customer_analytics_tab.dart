import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';

class CustomerAnalyticsTab extends StatelessWidget {
  const CustomerAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'Something went wrong',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        final customerAnalytics = state.customerAnalytics;
        if (customerAnalytics == null) {
          return const Center(child: Text('No customer data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCards(context, customerAnalytics),
              const SizedBox(height: 24),
              _buildCustomerSegments(context, customerAnalytics),
              const SizedBox(height: 24),
              _buildLocationDistribution(context, customerAnalytics),
              const SizedBox(height: 24),
              _buildRecentActivity(context, customerAnalytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(BuildContext context, CustomerAnalytics analytics) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          context,
          'Total Customers',
          analytics.totalCustomers.toString(),
          Icons.people,
          Colors.blue,
        ),
        _buildMetricCard(
          context,
          'New Customers',
          analytics.newCustomers.toString(),
          Icons.person_add,
          Colors.green,
        ),
        _buildMetricCard(
          context,
          'Retention Rate',
          '${analytics.customerRetentionRate.toStringAsFixed(1)}%',
          Icons.repeat,
          Colors.purple,
        ),
        _buildMetricCard(
          context,
          'Avg. Customer Value',
          CurrencyFormatter.format(analytics.averageCustomerLifetimeValue),
          Icons.monetization_on,
          Colors.orange,
        ),
      ],
    );
  }

  Widget _buildMetricCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSegments(
      BuildContext context, CustomerAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customer Segments',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.segments.map((segment) => ListTile(
                  title: Text(segment.name),
                  subtitle: Text(
                      '${segment.count} customers • Avg. Order: ${CurrencyFormatter.format(segment.averageOrderValue)}'),
                  trailing: Text(
                    CurrencyFormatter.format(segment.revenue),
                    style: TextStyle(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationDistribution(
      BuildContext context, CustomerAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Customers by Location',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.customersByLocation.entries.map((entry) {
              final percentage = (entry.value /
                      analytics.customersByLocation.values
                          .reduce((a, b) => a + b) *
                      100)
                  .toStringAsFixed(1);
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(entry.key),
                        Text('$percentage% (${entry.value})'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: entry.value /
                          analytics.customersByLocation.values
                              .reduce((a, b) => a + b),
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.blue.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(
      BuildContext context, CustomerAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activity',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.recentActivity.map((activity) => ListTile(
                  title: Text(activity.action),
                  subtitle: Text(
                    DateFormatter.formatDateTime(activity.timestamp),
                  ),
                  trailing: Text(
                    'ID: ${activity.customerId.substring(0, 8)}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                )),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:shivish/shared/models/booking/booking_model.dart';
import 'package:shivish/apps/technician/providers/booking_provider.dart';
import 'package:shivish/apps/technician/providers/technician_provider.dart';
import 'package:shivish/apps/technician/widgets/technician_app_toolbar.dart';
import 'package:shivish/apps/technician/technician_routes.dart';

class BookingListScreen extends ConsumerWidget {
  const BookingListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final technicianState = ref.watch(technicianProvider);
    final selectedStatus = ValueNotifier<BookingStatus?>(null);

    return Scaffold(
      appBar: TechnicianAppToolbar.simple(
        title: 'My Bookings',
        fallbackRoute: TechnicianRoutes.home,
        actions: [
          IconButton(
            icon: const Icon(Icons.calendar_today),
            onPressed: () {
              context.go(TechnicianRoutes.bookingCalendar);
            },
          ),
        ],
      ),
      body: technicianState.when(
        data: (technician) {
          if (technician == null) {
            return const Center(
              child: Text('No technician data available'),
            );
          }

          return Column(
            children: [
              _buildStatusFilter(selectedStatus),
              Expanded(
                child: ValueListenableBuilder<BookingStatus?>(
                  valueListenable: selectedStatus,
                  builder: (context, status, child) {
                    final bookingsProvider = status == null
                        ? technicianBookingsProvider(technician.id)
                        : technicianBookingsByStatusProvider(
                            (technicianId: technician.id, status: status),
                          );

                    return ref.watch(bookingsProvider).when(
                          data: (bookings) {
                            if (bookings.isEmpty) {
                              return const Center(
                                child: Text('No bookings found'),
                              );
                            }

                            return ListView.builder(
                              padding: const EdgeInsets.all(16),
                              itemCount: bookings.length,
                              itemBuilder: (context, index) {
                                final booking = bookings[index];
                                return _buildBookingCard(context, booking);
                              },
                            );
                          },
                          loading: () => const Center(
                            child: CircularProgressIndicator(),
                          ),
                          error: (error, stackTrace) => Center(
                            child: Text('Error: $error'),
                          ),
                        );
                  },
                ),
              ),
            ],
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) => Center(
          child: Text('Error: $error'),
        ),
      ),
    );
  }

  Widget _buildStatusFilter(ValueNotifier<BookingStatus?> selectedStatus) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            FilterChip(
              label: const Text('All'),
              selected: selectedStatus.value == null,
              onSelected: (selected) {
                selectedStatus.value = null;
              },
            ),
            const SizedBox(width: 8),
            ...BookingStatus.values.map((status) {
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(status.name.toUpperCase()),
                  selected: selectedStatus.value == status,
                  onSelected: (selected) {
                    selectedStatus.value = selected ? status : null;
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildBookingCard(BuildContext context, BookingModel booking) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: ListTile(
        title: Text(
          booking.services.isNotEmpty ? booking.services.first : 'Service',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${booking.startTime.hour}:${booking.startTime.minute.toString().padLeft(2, '0')} - ${booking.endTime.hour}:${booking.endTime.minute.toString().padLeft(2, '0')}',
            ),
            const SizedBox(height: 4),
            Text(booking.serviceLocation.contactName),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 8,
            vertical: 4,
          ),
          decoration: BoxDecoration(
            color: _getStatusColor(booking.status).withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            booking.status.name.toUpperCase(),
            style: TextStyle(
              color: _getStatusColor(booking.status),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () {
          context.go('${TechnicianRoutes.bookingDetails}/${booking.id}');
        },
      ),
    );
  }

  Color _getStatusColor(BookingStatus status) {
    switch (status) {
      case BookingStatus.pending:
        return Colors.orange;
      case BookingStatus.confirmed:
        return Colors.blue;
      case BookingStatus.inProgress:
        return Colors.purple;
      case BookingStatus.completed:
        return Colors.green;
      case BookingStatus.cancelled:
        return Colors.red;
      case BookingStatus.rescheduled:
        return Colors.amber;
      case BookingStatus.noShow:
        return Colors.grey;
      case BookingStatus.rejected:
        return Colors.redAccent;
    }
  }
}

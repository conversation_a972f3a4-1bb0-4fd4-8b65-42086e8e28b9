{"name": "shivish-auth-service", "version": "1.0.0", "description": "Custom JWT-based authentication service for Shivish e-commerce platform", "main": "dist/app.js", "scripts": {"dev": "nodemon --exec \"ts-node --esm src/app.ts\"", "build": "tsc", "start": "node dist/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "db:setup": "ts-node scripts/setup-database.ts", "db:migrate": "ts-node scripts/migrate.ts", "db:seed": "ts-node scripts/seed.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "deploy:dev": "npm run build && firebase deploy --only functions:authService --project dev", "deploy:prod": "npm run build && firebase deploy --only functions:authService --project prod"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "argon2": "^0.31.2", "pg": "^8.11.3", "redis": "^4.6.10", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "joi": "^17.11.0", "axios": "^1.6.2", "cookie-parser": "^1.4.6", "express-async-errors": "^3.1.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/jsonwebtoken": "^9.0.5", "@types/pg": "^8.10.9", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/cookie-parser": "^1.4.6", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.2", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["authentication", "jwt", "express", "typescript", "e-commerce", "security"], "author": "Shivish Team", "license": "MIT"}
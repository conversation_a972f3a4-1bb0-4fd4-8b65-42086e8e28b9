import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/utils/logger.dart';
import '../../services/saviour_notification_service.dart';
import '../../saviour_routes.dart';

final _logger = getLogger('NotificationScreen');

/// Provider for notifications
final notificationsProvider = StreamProvider<List<Map<String, dynamic>>>((ref) {
  final notificationService = ref.watch(saviourNotificationServiceProvider);
  return notificationService.getNotifications();
});

/// Provider for unread notifications count
final unreadNotificationsCountProvider = StreamProvider<int>((ref) {
  final notificationService = ref.watch(saviourNotificationServiceProvider);
  return notificationService.getUnreadNotificationsCount();
});

class NotificationScreen extends ConsumerWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notificationsAsync = ref.watch(notificationsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Use pop() to go back to previous screen
            context.pop();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.done_all),
            tooltip: 'Mark all as read',
            onPressed: () async {
              try {
                await ref
                    .read(saviourNotificationServiceProvider)
                    .markAllNotificationsAsRead();

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('All notifications marked as read'),
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error marking all notifications as read: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.delete_sweep),
            tooltip: 'Clear all notifications',
            onPressed: () {
              _showClearConfirmationDialog(context, ref);
            },
          ),
        ],
      ),
      body: notificationsAsync.when(
        data: (notifications) {
          if (notifications.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.notifications_off,
                    size: 64,
                    color: Colors.grey,
                  ),
                  SizedBox(height: 16),
                  Text(
                    'No notifications',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return _buildNotificationItem(context, ref, notification);
            },
          );
        },
        loading: () => const Center(
          child: CircularProgressIndicator(),
        ),
        error: (error, stackTrace) {
          _logger.severe('Error loading notifications: $error\n$stackTrace');
          return Center(
            child: Text('Error loading notifications: $error'),
          );
        },
      ),
    );
  }

  Widget _buildNotificationItem(
    BuildContext context,
    WidgetRef ref,
    Map<String, dynamic> notification,
  ) {
    final theme = Theme.of(context);
    final isRead = notification['isRead'] ?? false;
    final type = notification['type'] as String? ?? 'general';
    final createdAt = notification['createdAt'] != null
        ? (notification['createdAt'] as dynamic).toDate()
        : DateTime.now();

    return Dismissible(
      key: Key(notification['id']),
      background: Container(
        color: Colors.red,
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16.0),
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      direction: DismissDirection.endToStart,
      onDismissed: (direction) {
        ref
            .read(saviourNotificationServiceProvider)
            .deleteNotification(notification['id']);
      },
      child: InkWell(
        onTap: () {
          // Mark as read
          if (!isRead) {
            ref
                .read(saviourNotificationServiceProvider)
                .markNotificationAsRead(notification['id']);
          }

          // Handle notification tap based on type
          _handleNotificationTap(context, notification);
        },
        child: Container(
          color: isRead ? null : theme.colorScheme.primary.withOpacity(0.05),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: _getNotificationColor(type).withOpacity(0.1),
              child: Icon(
                _getNotificationIcon(type),
                color: _getNotificationColor(type),
              ),
            ),
            title: Text(
              notification['title'] ?? 'Notification',
              style: TextStyle(
                fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(notification['body'] ?? ''),
                const SizedBox(height: 4),
                Text(
                  _formatDateTime(createdAt),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            isThreeLine: true,
            trailing: !isRead
                ? Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: theme.colorScheme.primary,
                    ),
                  )
                : null,
          ),
        ),
      ),
    );
  }

  void _handleNotificationTap(
      BuildContext context, Map<String, dynamic> notification) {
    final type = notification['type'] as String? ?? 'general';
    final data = notification['data'] as Map<String, dynamic>? ?? {};

    switch (type) {
      case 'new_request':
        if (data['requestId'] != null) {
          context.go(SaviourRoutes.deliveryRequests);
        }
        break;
      case 'delivery_update':
        if (data['requestId'] != null) {
          context.go(SaviourRoutes.getActiveDeliveryRoute(data['requestId']));
        }
        break;
      case 'earnings':
        // Navigate to earnings screen (to be implemented)
        break;
      default:
        // Just mark as read
        break;
    }
  }

  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'new_request':
        return Icons.local_shipping;
      case 'delivery_update':
        return Icons.delivery_dining;
      case 'earnings':
        return Icons.attach_money;
      default:
        return Icons.notifications;
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'new_request':
        return Colors.blue;
      case 'delivery_update':
        return Colors.green;
      case 'earnings':
        return Colors.orange;
      default:
        return Colors.purple;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateOnly == today) {
      return 'Today, ${DateFormat('h:mm a').format(dateTime)}';
    } else if (dateOnly == yesterday) {
      return 'Yesterday, ${DateFormat('h:mm a').format(dateTime)}';
    } else {
      return DateFormat('MMM d, h:mm a').format(dateTime);
    }
  }

  void _showClearConfirmationDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Notifications'),
        content: const Text(
            'Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);

              try {
                await ref
                    .read(saviourNotificationServiceProvider)
                    .clearAllNotifications();

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('All notifications cleared'),
                    ),
                  );
                }
              } catch (e) {
                _logger.severe('Error clearing all notifications: $e');

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}

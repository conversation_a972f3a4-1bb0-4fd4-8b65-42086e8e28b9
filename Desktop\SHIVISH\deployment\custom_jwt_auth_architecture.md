# Custom JWT Authentication Architecture

## Overview
Replace Firebase Auth with a custom JWT-based authentication system similar to major e-commerce platforms like Amazon and Flipkart.

## Architecture Design

### 1. Authentication Service (Node.js/Express)
```
/auth-service
├── src/
│   ├── controllers/
│   │   ├── auth.controller.ts
│   │   ├── user.controller.ts
│   │   └── social.controller.ts
│   ├── middleware/
│   │   ├── auth.middleware.ts
│   │   ├── rate-limit.middleware.ts
│   │   └── validation.middleware.ts
│   ├── models/
│   │   ├── user.model.ts
│   │   ├── session.model.ts
│   │   └── auth.types.ts
│   ├── services/
│   │   ├── jwt.service.ts
│   │   ├── password.service.ts
│   │   ├── email.service.ts
│   │   └── social.service.ts
│   ├── utils/
│   │   ├── crypto.utils.ts
│   │   ├── validation.utils.ts
│   │   └── logger.utils.ts
│   └── app.ts
├── package.json
└── Dockerfile
```

### 2. Database Schema (PostgreSQL)
```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    phone_number VARCHAR(20),
    photo_url TEXT,
    role VARCHAR(50) NOT NULL DEFAULT 'buyer',
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    last_login_at TIMESTAMP,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMP
);

-- Sessions table
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    device_info JSONB,
    ip_address INET,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    last_used_at TIMESTAMP DEFAULT NOW()
);

-- Social accounts table
CREATE TABLE social_accounts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    provider VARCHAR(50) NOT NULL,
    provider_id VARCHAR(255) NOT NULL,
    provider_data JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(provider, provider_id)
);

-- Password reset tokens
CREATE TABLE password_reset_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 3. JWT Token Structure
```typescript
interface AccessToken {
  sub: string;        // user ID
  email: string;      // user email
  role: string;       // user role
  iat: number;        // issued at
  exp: number;        // expires at (15 minutes)
  iss: string;        // issuer
  aud: string;        // audience
}

interface RefreshToken {
  sub: string;        // user ID
  jti: string;        // token ID (session ID)
  iat: number;        // issued at
  exp: number;        // expires at (30 days)
  iss: string;        // issuer
  aud: string;        // audience
}
```

### 4. Security Features
- **Password Hashing**: Argon2id (industry standard)
- **Rate Limiting**: 5 attempts per minute per IP
- **Account Lockout**: 5 failed attempts = 15 min lockout
- **JWT Rotation**: Access tokens expire in 15 minutes
- **Refresh Token**: Secure, httpOnly, 30-day expiry
- **CSRF Protection**: SameSite cookies
- **Input Validation**: Joi/Zod validation
- **SQL Injection**: Parameterized queries

### 5. API Endpoints
```
POST /auth/register
POST /auth/login
POST /auth/refresh
POST /auth/logout
POST /auth/forgot-password
POST /auth/reset-password
POST /auth/verify-email
POST /auth/resend-verification

POST /auth/social/google
POST /auth/social/apple

GET  /auth/me
PUT  /auth/profile
DELETE /auth/account
```

### 6. Flutter Client Architecture
```
/lib/shared/auth/
├── models/
│   ├── auth_user.dart
│   ├── auth_tokens.dart
│   └── auth_response.dart
├── services/
│   ├── auth_service.dart
│   ├── token_service.dart
│   └── secure_storage.dart
├── repositories/
│   ├── auth_repository.dart
│   └── auth_repository_impl.dart
├── providers/
│   ├── auth_provider.dart
│   └── auth_state.dart
└── interceptors/
    ├── auth_interceptor.dart
    └── token_refresh_interceptor.dart
```

## Implementation Benefits

### Cost Savings
- **Firebase Auth**: $275/month (100K users)
- **Custom JWT**: $10-20/month (server costs only)
- **Savings**: 95% cost reduction

### Performance Benefits
- **Faster Authentication**: Direct database queries
- **Reduced Latency**: No external API calls
- **Better Caching**: Custom caching strategies
- **Offline Support**: Local token validation

### Control Benefits
- **Custom Business Logic**: Role-based permissions
- **Advanced Security**: Custom security policies
- **Analytics**: Detailed auth analytics
- **Compliance**: GDPR/SOC2 compliance

## Migration Strategy

### Phase 1: Build Auth Service (Week 1)
1. Set up Node.js/Express auth service
2. Create PostgreSQL database schema
3. Implement JWT token generation/validation
4. Add password hashing and security features

### Phase 2: Flutter Integration (Week 2)
1. Create Flutter auth client
2. Implement secure token storage
3. Add HTTP interceptors for token refresh
4. Update UI to use new auth service

### Phase 3: Replace Firebase Auth (Week 3)
1. Remove Firebase Auth dependencies
2. Update all authentication calls
3. Migrate existing users (if needed)
4. Test all authentication flows

### Phase 4: Advanced Features (Week 4)
1. Add social login support
2. Implement advanced security features
3. Add monitoring and analytics
4. Performance optimization

## Build Commands

### Development Setup
```bash
# Install dependencies
npm install

# Set up database
npm run db:setup
npm run db:migrate

# Start development server
npm run dev:auth-service
flutter run --flavor dev
```

### Production Deployment
```bash
# Build auth service
npm run build:auth-service

# Deploy to production
npm run deploy:auth-service

# Build Flutter apps
flutter build apk --release
flutter build ios --release
flutter build web --release
```

### Testing
```bash
# Run auth service tests
npm run test:auth-service

# Run Flutter tests
flutter test

# Run integration tests
npm run test:integration
```

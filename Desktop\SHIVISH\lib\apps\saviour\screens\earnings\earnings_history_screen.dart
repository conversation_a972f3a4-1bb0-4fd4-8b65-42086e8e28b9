import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import '../../../../shared/utils/logger.dart';
import '../../models/earnings.dart';
import '../../providers/earnings_provider.dart';

final _logger = getLogger('EarningsHistoryScreen');

class EarningsHistoryScreen extends ConsumerStatefulWidget {
  const EarningsHistoryScreen({super.key});

  @override
  ConsumerState<EarningsHistoryScreen> createState() => _EarningsHistoryScreenState();
}

class _EarningsHistoryScreenState extends ConsumerState<EarningsHistoryScreen> {
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  String? _selectedStatus;
  String? _selectedType;
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final earningsAsync = ref.watch(earningsListProvider({
      'startDate': _startDate,
      'endDate': _endDate,
      'status': _selectedStatus,
      'type': _selectedType,
    }));
    
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(earningsListProvider);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filter card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Filter Earnings',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Date range
                    Row(
                      children: [
                        Expanded(
                          child: _buildDatePicker(
                            context,
                            'Start Date',
                            _startDate,
                            (date) {
                              setState(() {
                                _startDate = date;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildDatePicker(
                            context,
                            'End Date',
                            _endDate,
                            (date) {
                              setState(() {
                                _endDate = date;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Status and type filters
                    Row(
                      children: [
                        Expanded(
                          child: _buildDropdown(
                            'Status',
                            _selectedStatus,
                            [
                              'All',
                              'Pending',
                              'Paid',
                              'Cancelled',
                            ],
                            (value) {
                              setState(() {
                                _selectedStatus = value == 'All' ? null : value;
                              });
                            },
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: _buildDropdown(
                            'Type',
                            _selectedType,
                            [
                              'All',
                              'Delivery',
                              'Bonus',
                              'Incentive',
                            ],
                            (value) {
                              setState(() {
                                _selectedType = value == 'All' ? null : value;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Apply filter button
                    FilledButton(
                      onPressed: () {
                        // Refresh the earnings list
                        ref.invalidate(earningsListProvider);
                      },
                      style: FilledButton.styleFrom(
                        minimumSize: const Size(double.infinity, 48),
                      ),
                      child: const Text('Apply Filters'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Earnings list
            earningsAsync.when(
              data: (earnings) {
                if (earnings.isEmpty) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.search_off,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No earnings found for the selected filters',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
                  );
                }
                
                return _buildEarningsList(theme, earnings);
              },
              loading: () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              ),
              error: (error, stackTrace) {
                _logger.severe('Error loading earnings: $error\n$stackTrace');
                return Center(
                  child: Padding(
                    padding: const EdgeInsets.all(32.0),
                    child: Text('Error loading earnings: $error'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDatePicker(
    BuildContext context,
    String label,
    DateTime initialDate,
    Function(DateTime) onDateSelected,
  ) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: initialDate,
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
        );
        
        if (date != null) {
          onDateSelected(date);
        }
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.calendar_today, size: 16),
                const SizedBox(width: 8),
                Text(
                  DateFormat('MMM d, yyyy').format(initialDate),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDropdown(
    String label,
    String? selectedValue,
    List<String> options,
    Function(String?) onChanged,
  ) {
    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      value: selectedValue ?? 'All',
      items: options.map((option) {
        return DropdownMenuItem<String>(
          value: option,
          child: Text(option),
        );
      }).toList(),
      onChanged: onChanged,
    );
  }
  
  Widget _buildEarningsList(ThemeData theme, List<Earnings> earnings) {
    // Group earnings by date
    final Map<String, List<Earnings>> groupedEarnings = {};
    
    for (final earning in earnings) {
      final dateStr = DateFormat('yyyy-MM-dd').format(earning.timestamp);
      
      if (!groupedEarnings.containsKey(dateStr)) {
        groupedEarnings[dateStr] = [];
      }
      
      groupedEarnings[dateStr]!.add(earning);
    }
    
    // Sort dates in descending order
    final sortedDates = groupedEarnings.keys.toList()
      ..sort((a, b) => b.compareTo(a));
    
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateStr = sortedDates[index];
        final dateEarnings = groupedEarnings[dateStr]!;
        final date = DateTime.parse(dateStr);
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Text(
                DateFormat('EEEE, MMMM d, yyyy').format(date),
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Card(
              child: ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: dateEarnings.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final earning = dateEarnings[index];
                  return _buildEarningItem(theme, earning);
                },
              ),
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
  
  Widget _buildEarningItem(ThemeData theme, Earnings earning) {
    final timeStr = DateFormat('h:mm a').format(earning.timestamp);
    
    // Determine icon and color based on type
    IconData icon;
    Color color;
    
    switch (earning.type.toLowerCase()) {
      case 'delivery':
        icon = Icons.delivery_dining;
        color = Colors.blue;
        break;
      case 'bonus':
        icon = Icons.star;
        color = Colors.orange;
        break;
      case 'incentive':
        icon = Icons.trending_up;
        color = Colors.green;
        break;
      default:
        icon = Icons.attach_money;
        color = Colors.purple;
    }
    
    return ListTile(
      leading: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: color,
        ),
      ),
      title: Row(
        children: [
          Text(
            '₹${earning.amount.toStringAsFixed(2)}',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(width: 8),
          _buildStatusChip(earning.status),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order #${earning.orderId}',
            style: theme.textTheme.bodySmall,
          ),
          Text(
            '${earning.type} • $timeStr',
            style: theme.textTheme.bodySmall,
          ),
        ],
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: () {
        // Show earning details
        _showEarningDetails(earning);
      },
    );
  }
  
  Widget _buildStatusChip(String status) {
    late Color color;
    late String label;
    
    switch (status.toLowerCase()) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'paid':
        color = Colors.green;
        label = 'Paid';
        break;
      case 'cancelled':
        color = Colors.red;
        label = 'Cancelled';
        break;
      default:
        color = Colors.grey;
        label = status;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  void _showEarningDetails(Earnings earning) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Earning Details',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              const SizedBox(height: 16),
              _buildDetailRow('Amount', '₹${earning.amount.toStringAsFixed(2)}'),
              _buildDetailRow('Status', earning.status),
              _buildDetailRow('Type', earning.type),
              _buildDetailRow('Order ID', '#${earning.orderId}'),
              _buildDetailRow('Delivery ID', '#${earning.deliveryId}'),
              _buildDetailRow(
                'Date & Time',
                DateFormat('MMM d, yyyy • h:mm a').format(earning.timestamp),
              ),
              if (earning.status.toLowerCase() == 'paid' && earning.paidAt != null)
                _buildDetailRow(
                  'Paid On',
                  DateFormat('MMM d, yyyy • h:mm a').format(earning.paidAt!),
                ),
              if (earning.transactionId != null)
                _buildDetailRow('Transaction ID', earning.transactionId!),
              if (earning.notes != null) _buildDetailRow('Notes', earning.notes!),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 16),
              if (earning.status.toLowerCase() == 'pending')
                Center(
                  child: Text(
                    'This earning will be included in your next payout',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
  
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

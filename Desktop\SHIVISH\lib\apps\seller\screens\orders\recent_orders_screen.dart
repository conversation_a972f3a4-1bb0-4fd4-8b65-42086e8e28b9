import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/presentation/cubits/orders_cubit.dart'
    as cubit;
import 'package:shivish/shared/ui_components/loading/loading_indicator.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:shivish/shared/ui_components/toolbar/app_toolbar.dart';

class RecentOrdersScreen extends StatelessWidget {
  const RecentOrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<cubit.OrdersCubit, cubit.OrdersState>(
      builder: (context, state) {
        return Scaffold(
          appBar: AppToolbar.simple(
            title: 'Recent Orders',
          ),
          body: state.maybeWhen(
            loading: () => const LoadingIndicator(),
            error: (message) => Center(
              child: Text(
                'Error: $message',
                style: const TextStyle(color: Colors.red),
              ),
            ),
            loaded: (orders) => _buildContent(context, orders),
            orElse: () => const Center(child: Text('Unknown State')),
          ),
        );
      },
    );
  }

  Widget _buildContent(BuildContext context, List<cubit.Order> orders) {
    if (orders.isEmpty) {
      return const Center(
        child: Text('No recent orders'),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          child: ListTile(
            title: Text('Order #${order.id}'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${order.items.length} items • ${CurrencyFormatter.format(order.totalAmount)}',
                ),
                Text(
                  DateFormatter.formatDateTime(order.createdAt),
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            trailing: _buildOrderStatusChip(context, order.status),
            onTap: () => _navigateToOrderDetails(context, order),
          ),
        );
      },
    );
  }

  Widget _buildOrderStatusChip(BuildContext context, cubit.OrderStatus status) {
    final color = _getStatusColor(status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.name,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(cubit.OrderStatus status) {
    switch (status) {
      case cubit.OrderStatus.pending:
        return Colors.orange;
      case cubit.OrderStatus.processing:
        return Colors.blue;
      case cubit.OrderStatus.shipped:
        return Colors.purple;
      case cubit.OrderStatus.delivered:
        return Colors.green;
      case cubit.OrderStatus.cancelled:
        return Colors.red;
    }
  }

  void _navigateToOrderDetails(BuildContext context, cubit.Order order) {
    Navigator.pushNamed(
      context,
      '/seller/orders/${order.id}',
      arguments: order,
    );
  }
}

enum OrderStatus {
  pending,
  processing,
  shipped,
  delivered,
  cancelled,
}

class Order {
  final String id;
  final List<OrderItem> items;
  final double totalAmount;
  final DateTime createdAt;
  final OrderStatus status;
  final String customerId;
  final String customerName;
  final String shippingAddress;

  const Order({
    required this.id,
    required this.items,
    required this.totalAmount,
    required this.createdAt,
    required this.status,
    required this.customerId,
    required this.customerName,
    required this.shippingAddress,
  });
}

class OrderItem {
  final String id;
  final String name;
  final int quantity;
  final double price;

  const OrderItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.price,
  });
}

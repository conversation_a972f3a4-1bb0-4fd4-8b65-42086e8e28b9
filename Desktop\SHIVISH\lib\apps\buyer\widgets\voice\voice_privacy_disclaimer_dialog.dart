import 'package:flutter/material.dart';

/// A dialog that displays a privacy disclaimer for voice assistant usage
class VoicePrivacyDisclaimerDialog extends StatelessWidget {
  final VoidCallback onAccept;
  final VoidCallback onDecline;

  const VoicePrivacyDisclaimerDialog({
    super.key,
    required this.onAccept,
    required this.onDecline,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.privacy_tip,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          const Text('Voice Privacy Notice'),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'SHIVISH Voice Assistant Privacy',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'By enabling the voice assistant, you agree to the following:',
            ),
            const SizedBox(height: 8),
            _buildBulletPoint(
              context,
              'SHIVISH will NOT save your voice inputs in any external database.',
            ),
            _buildBulletPoint(
              context,
              'Your voice data will NOT be used for training AI models or any other purposes beyond your immediate request.',
            ),
            _buildBulletPoint(
              context,
              'All voice processing happens on your device whenever possible.',
            ),
            _buildBulletPoint(
              context,
              'Any data saved will be stored only on your mobile device for AI predictions to help you with personalized assistance.',
            ),
            _buildBulletPoint(
              context,
              'You can disable the voice assistant at any time from settings.',
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: theme.colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Your privacy is important to us. Thank you for trusting SHIVISH.',
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: onDecline,
          child: const Text('Decline'),
        ),
        ElevatedButton(
          onPressed: onAccept,
          style: ElevatedButton.styleFrom(
            backgroundColor: theme.colorScheme.primary,
            foregroundColor: theme.colorScheme.onPrimary,
          ),
          child: const Text('Accept & Enable'),
        ),
      ],
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(text)),
        ],
      ),
    );
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserService = void 0;
const database_service_1 = require("./database.service");
const password_service_1 = require("./password.service");
const logger_1 = require("../utils/logger");
const user_model_1 = require("../models/user.model");
class UserService {
    async getUserById(userId) {
        try {
            const result = await database_service_1.databaseService.query('SELECT * FROM users WHERE id = $1', [userId]);
            if (result.rows.length === 0) {
                throw new Error('User not found');
            }
            const user = user_model_1.UserMapper.fromRow(result.rows[0]);
            return user_model_1.UserMapper.toProfile(user);
        }
        catch (error) {
            logger_1.logger.error('Get user by ID failed:', error);
            throw error;
        }
    }
    async updateProfile(userId, updates) {
        try {
            const setParts = [];
            const values = [];
            let paramIndex = 1;
            if (updates.displayName !== undefined) {
                setParts.push(`display_name = $${paramIndex++}`);
                values.push(updates.displayName);
            }
            if (updates.phoneNumber !== undefined) {
                setParts.push(`phone_number = $${paramIndex++}`);
                values.push(updates.phoneNumber);
            }
            if (updates.photoUrl !== undefined) {
                setParts.push(`photo_url = $${paramIndex++}`);
                values.push(updates.photoUrl);
            }
            if (setParts.length === 0) {
                throw new Error('No updates provided');
            }
            setParts.push(`updated_at = NOW()`);
            values.push(userId);
            const query = `
        UPDATE users 
        SET ${setParts.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `;
            const result = await database_service_1.databaseService.query(query, values);
            if (result.rows.length === 0) {
                throw new Error('User not found');
            }
            const user = user_model_1.UserMapper.fromRow(result.rows[0]);
            return user_model_1.UserMapper.toProfile(user);
        }
        catch (error) {
            logger_1.logger.error('Update profile failed:', error);
            throw error;
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        try {
            const userResult = await database_service_1.databaseService.query('SELECT * FROM users WHERE id = $1', [userId]);
            if (userResult.rows.length === 0) {
                throw new Error('User not found');
            }
            const user = user_model_1.UserMapper.fromRow(userResult.rows[0]);
            const isCurrentPasswordValid = await password_service_1.passwordService.verifyPassword(currentPassword, user.passwordHash);
            if (!isCurrentPasswordValid) {
                throw new Error('Current password is incorrect');
            }
            const passwordValidation = password_service_1.passwordService.validatePassword(newPassword);
            if (!passwordValidation.isValid) {
                throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
            }
            const newPasswordHash = await password_service_1.passwordService.hashPassword(newPassword);
            await database_service_1.databaseService.query('UPDATE users SET password_hash = $1, updated_at = NOW() WHERE id = $2', [newPasswordHash, userId]);
        }
        catch (error) {
            logger_1.logger.error('Change password failed:', error);
            throw error;
        }
    }
    async deleteAccount(userId) {
        try {
            await database_service_1.databaseService.transaction(async (client) => {
                await client.query('DELETE FROM user_sessions WHERE user_id = $1', [userId]);
                await client.query('DELETE FROM password_reset_tokens WHERE user_id = $1', [userId]);
                await client.query('DELETE FROM social_accounts WHERE user_id = $1', [userId]);
                await client.query('DELETE FROM users WHERE id = $1', [userId]);
            });
        }
        catch (error) {
            logger_1.logger.error('Delete account failed:', error);
            throw error;
        }
    }
    async getUserSessions(userId) {
        try {
            const result = await database_service_1.databaseService.query(`SELECT id, device_info, ip_address, created_at, last_used_at, expires_at
         FROM user_sessions 
         WHERE user_id = $1 AND expires_at > NOW()
         ORDER BY last_used_at DESC`, [userId]);
            return result.rows.map(row => ({
                id: row.id,
                deviceInfo: row.device_info,
                ipAddress: row.ip_address,
                createdAt: row.created_at,
                lastUsedAt: row.last_used_at,
                expiresAt: row.expires_at,
            }));
        }
        catch (error) {
            logger_1.logger.error('Get user sessions failed:', error);
            throw error;
        }
    }
    async deleteSession(userId, sessionId) {
        try {
            const result = await database_service_1.databaseService.query('DELETE FROM user_sessions WHERE id = $1 AND user_id = $2', [sessionId, userId]);
            if (result.rowCount === 0) {
                throw new Error('Session not found');
            }
        }
        catch (error) {
            logger_1.logger.error('Delete session failed:', error);
            throw error;
        }
    }
}
exports.UserService = UserService;
//# sourceMappingURL=user.service.js.map
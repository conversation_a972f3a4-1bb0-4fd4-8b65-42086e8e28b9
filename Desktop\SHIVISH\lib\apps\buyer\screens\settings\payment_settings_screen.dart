import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/ui_components/navigation/back_button_handler.dart';
import '../../buyer_routes.dart';

class PaymentSettingsScreen extends ConsumerStatefulWidget {
  const PaymentSettingsScreen({super.key});

  @override
  ConsumerState<PaymentSettingsScreen> createState() =>
      _PaymentSettingsScreenState();
}

class _PaymentSettingsScreenState extends ConsumerState<PaymentSettingsScreen> {
  final List<PaymentMethod> _savedCards = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadPaymentMethods();
  }

  Future<void> _loadPaymentMethods() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate loading saved payment methods
    await Future.delayed(const Duration(milliseconds: 500));

    // Mock data for demonstration
    setState(() {
      _savedCards.addAll([
        PaymentMethod(
          id: '1',
          type: 'Visa',
          lastFour: '4242',
          expiryMonth: 12,
          expiryYear: 2025,
          isDefault: true,
        ),
        PaymentMethod(
          id: '2',
          type: 'Mastercard',
          lastFour: '5555',
          expiryMonth: 10,
          expiryYear: 2024,
          isDefault: false,
        ),
      ]);
      _isLoading = false;
    });
  }

  Future<void> _addNewCard() async {
    // Navigate to add card screen
    BuyerRoutes.navigateToAddCard(context);
  }

  Future<void> _removeCard(String id) async {
    setState(() {
      _savedCards.removeWhere((card) => card.id == id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Card removed successfully'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  Future<void> _setDefaultCard(String id) async {
    setState(() {
      for (var card in _savedCards) {
        card.isDefault = card.id == id;
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Default payment method updated'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return BackButtonHandler(
      fallbackRoute: BuyerRoutes.settings,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Payment Methods'),
          backgroundColor: theme.colorScheme.primary,
          foregroundColor: theme.colorScheme.onPrimary,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () {
              // Check if we can pop the current route
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop();
              } else {
                // If we can't pop, go to the settings screen
                context.go(BuyerRoutes.settings);
              }
            },
          ),
          systemOverlayStyle: SystemUiOverlayStyle(
            statusBarColor: Colors.transparent,
            statusBarIconBrightness: Brightness.light,
            statusBarBrightness: Brightness.dark,
          ),
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text(
                      'Saved Payment Methods',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: _savedCards.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.credit_card,
                                  size: 64,
                                  color: theme.colorScheme.secondary,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'No payment methods saved',
                                  style: theme.textTheme.titleLarge,
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'Add a payment method to get started',
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _savedCards.length,
                            itemBuilder: (context, index) {
                              final card = _savedCards[index];
                              return _buildCardTile(context, card);
                            },
                          ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: ElevatedButton.icon(
                      onPressed: _addNewCard,
                      icon: const Icon(Icons.add),
                      label: const Text('Add New Payment Method'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        minimumSize: const Size(double.infinity, 50),
                      ),
                    ),
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildCardTile(BuildContext context, PaymentMethod card) {
    final theme = Theme.of(context);
    final cardIcon = card.type == 'Visa'
        ? Icons.credit_card
        : card.type == 'Mastercard'
            ? Icons.credit_card
            : Icons.credit_card;

    return Dismissible(
      key: Key(card.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 16),
        color: theme.colorScheme.error,
        child: const Icon(
          Icons.delete,
          color: Colors.white,
        ),
      ),
      confirmDismiss: (direction) async {
        return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Remove Payment Method'),
            content: const Text(
                'Are you sure you want to remove this payment method?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Remove'),
              ),
            ],
          ),
        );
      },
      onDismissed: (direction) {
        _removeCard(card.id);
      },
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
          child: Icon(
            cardIcon,
            color: theme.colorScheme.primary,
          ),
        ),
        title: Text(
          '${card.type} •••• ${card.lastFour}',
          style: theme.textTheme.titleMedium,
        ),
        subtitle: Text(
          'Expires ${card.expiryMonth}/${card.expiryYear}',
          style: theme.textTheme.bodySmall,
        ),
        trailing: card.isDefault
            ? Chip(
                label: const Text('Default'),
                backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                labelStyle: TextStyle(
                  color: theme.colorScheme.primary,
                  fontSize: 12,
                ),
              )
            : TextButton(
                onPressed: () => _setDefaultCard(card.id),
                child: const Text('Set as Default'),
              ),
      ),
    );
  }
}

class PaymentMethod {
  final String id;
  final String type;
  final String lastFour;
  final int expiryMonth;
  final int expiryYear;
  bool isDefault;

  PaymentMethod({
    required this.id,
    required this.type,
    required this.lastFour,
    required this.expiryMonth,
    required this.expiryYear,
    this.isDefault = false,
  });
}

import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../buyer_routes.dart';
import 'banner_carousel.dart';

class ModernHeroSection extends StatelessWidget {
  const ModernHeroSection({
    super.key,
    this.height,
    this.padding = const EdgeInsets.symmetric(horizontal: 16),
  });

  final double? height;
  final EdgeInsetsGeometry padding;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenSize = MediaQuery.of(context).size;

    return Container(
      height: height ?? screenSize.height * 0.50,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            theme.colorScheme.primary,
            theme.colorScheme.primary.withAlpha(204), // ~0.8 opacity
            theme.colorScheme.surface,
          ],
          stops: const [0.0, 0.7, 1.0],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(30),
          bottomRight: Radius.circular(30),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Space for app bar
          SizedBox(height: MediaQuery.of(context).padding.top + kToolbarHeight),

          // Welcome message
          Padding(
            padding: padding,
            child: Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome to',
                      style: theme.textTheme.bodyLarge?.copyWith(
                        color: Colors.white.withAlpha(230), // ~0.9 opacity
                      ),
                    ),
                    Text(
                      'SHIVISH',
                      style: theme.textTheme.headlineMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                // Notification bell
                Container(
                  decoration: BoxDecoration(
                    // ~0.2 opacity
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.all(8),
                  child: Image.asset(
                    'assets/images/flavors/notification.png',
                    width: 25,
                    height: 25,
                    fit: BoxFit.contain,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 2),

          // Search bar
          Padding(
            padding: padding,
            child: GestureDetector(
              onTap: () {
                // Navigate to search screen
                context.push(BuyerRoutes.search);
              },
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(26), // ~0.1 opacity
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12),
                      child: Icon(Icons.search, color: Colors.grey),
                    ),
                    Text(
                      'Search products...',
                      style: TextStyle(
                        color: Colors.grey.shade400,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Banner carousel
          const Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: BannerCarousel(),
            ),
          ),
        ],
      ),
    );
  }
}

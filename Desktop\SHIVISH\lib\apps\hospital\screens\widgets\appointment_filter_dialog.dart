import 'package:flutter/material.dart';
import 'package:shivish/shared/models/hospital/appointment_model.dart';

class AppointmentFilterDialog extends StatefulWidget {
  final AppointmentStatus? initialStatus;

  const AppointmentFilterDialog({
    super.key,
    this.initialStatus,
  });

  @override
  State<AppointmentFilterDialog> createState() => _AppointmentFilterDialogState();
}

class _AppointmentFilterDialogState extends State<AppointmentFilterDialog> {
  late AppointmentStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.initialStatus;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Appointments'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Status',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildStatusFilterChip(null, 'All'),
              _buildStatusFilterChip(AppointmentStatus.pending, 'Pending'),
              _buildStatusFilterChip(AppointmentStatus.confirmed, 'Confirmed'),
              _buildStatusFilterChip(AppointmentStatus.completed, 'Completed'),
              _buildStatusFilterChip(AppointmentStatus.cancelled, 'Cancelled'),
              _buildStatusFilterChip(AppointmentStatus.rescheduled, 'Rescheduled'),
              _buildStatusFilterChip(AppointmentStatus.noShow, 'No Show'),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context, _selectedStatus);
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Widget _buildStatusFilterChip(AppointmentStatus? status, String label) {
    final isSelected = _selectedStatus == status;
    
    Color chipColor;
    if (status == null) {
      chipColor = Colors.grey;
    } else {
      switch (status) {
        case AppointmentStatus.confirmed:
          chipColor = Colors.blue;
          break;
        case AppointmentStatus.completed:
          chipColor = Colors.green;
          break;
        case AppointmentStatus.pending:
          chipColor = Colors.orange;
          break;
        case AppointmentStatus.cancelled:
          chipColor = Colors.red;
          break;
        case AppointmentStatus.rescheduled:
          chipColor = Colors.purple;
          break;
        case AppointmentStatus.noShow:
          chipColor = Colors.grey;
          break;
      }
    }

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedStatus = selected ? status : null;
        });
      },
      backgroundColor: chipColor.withOpacity(0.1),
      selectedColor: chipColor.withOpacity(0.2),
      checkmarkColor: chipColor,
      labelStyle: TextStyle(
        color: isSelected ? chipColor : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? chipColor : Colors.grey.shade300,
      ),
    );
  }
}

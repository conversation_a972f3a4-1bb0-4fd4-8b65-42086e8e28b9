import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../../shared/models/delivery/delivery_request_model.dart';
import '../../../../shared/utils/distance_calculator.dart';

class DeliveryRequestCard extends StatelessWidget {
  final DeliveryRequestModel request;
  final bool isActive;
  final VoidCallback onTap;

  const DeliveryRequestCard({
    super.key,
    required this.request,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Calculate distance between pickup and delivery
    final distance = DistanceCalculator.calculateDistance(
      request.pickupLocation.latitude,
      request.pickupLocation.longitude,
      request.deliveryLocation.latitude,
      request.deliveryLocation.longitude,
    );

    // Format distance
    final formattedDistance = distance < 1000
        ? '${distance.toStringAsFixed(0)} m'
        : '${(distance / 1000).toStringAsFixed(1)} km';

    // Format time
    final formattedTime = DateFormat('h:mm a').format(
      isActive ? request.updatedAt : request.createdAt,
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isActive
            ? BorderSide(color: theme.colorScheme.primary, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Order ID and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Order #${request.orderId.substring(0, 8)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(request.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      _getStatusText(request.status),
                      style: TextStyle(
                        color: _getStatusColor(request.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Pickup and delivery locations
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.green,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      request.pickupAddress,
                      style: theme.textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.arrow_downward,
                    size: 16,
                    color: Colors.grey,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    formattedDistance,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  const Icon(
                    Icons.location_on,
                    size: 16,
                    color: Colors.red,
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      request.deliveryAddress,
                      style: theme.textTheme.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Order details
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Items and amount
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${request.itemCount ?? 0} items',
                        style: theme.textTheme.bodySmall,
                      ),
                      Text(
                        '₹${(request.orderTotal ?? 0.0).toStringAsFixed(2)}',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),

                  // Time and action button
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        formattedTime,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(height: 4),
                      ElevatedButton(
                        onPressed: onTap,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: isActive
                              ? theme.colorScheme.primary
                              : theme.colorScheme.secondary,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          minimumSize: const Size(0, 32),
                          textStyle: const TextStyle(fontSize: 12),
                        ),
                        child: Text(isActive ? 'View Details' : 'Accept'),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getStatusText(DeliveryRequestStatus status) {
    switch (status) {
      case DeliveryRequestStatus.pending:
        return 'Pending';
      case DeliveryRequestStatus.accepted:
        return 'Accepted';
      case DeliveryRequestStatus.pickedUp:
        return 'Picked Up';
      case DeliveryRequestStatus.inTransit:
        return 'In Transit';
      case DeliveryRequestStatus.delivered:
        return 'Delivered';
      case DeliveryRequestStatus.cancelled:
        return 'Cancelled';
      case DeliveryRequestStatus.failed:
        return 'Failed';
    }
  }

  Color _getStatusColor(DeliveryRequestStatus status) {
    switch (status) {
      case DeliveryRequestStatus.pending:
        return Colors.grey;
      case DeliveryRequestStatus.accepted:
        return Colors.blue;
      case DeliveryRequestStatus.pickedUp:
        return Colors.orange;
      case DeliveryRequestStatus.inTransit:
        return Colors.purple;
      case DeliveryRequestStatus.delivered:
        return Colors.green;
      case DeliveryRequestStatus.cancelled:
        return Colors.red;
      case DeliveryRequestStatus.failed:
        return Colors.red;
    }
  }
}

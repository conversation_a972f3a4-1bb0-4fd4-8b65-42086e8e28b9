import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/analytics_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/analytics_cubit.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';

class ProductAnalyticsTab extends StatelessWidget {
  const ProductAnalyticsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AnalyticsCubit, AnalyticsState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.hasError) {
          return Center(
            child: Text(
              state.errorMessage ?? 'Something went wrong',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          );
        }

        final productAnalytics = state.productAnalytics;
        if (productAnalytics == null) {
          return const Center(child: Text('No product data available'));
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSummaryCards(context, productAnalytics),
              const SizedBox(height: 24),
              _buildTopProducts(context, productAnalytics),
              const SizedBox(height: 24),
              _buildLowStockProducts(context, productAnalytics),
              const SizedBox(height: 24),
              _buildCategoryDistribution(context, productAnalytics),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSummaryCards(BuildContext context, ProductAnalytics analytics) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        _buildMetricCard(
          context,
          'Total Products',
          analytics.totalProducts.toString(),
          Icons.inventory_2,
          Colors.blue,
        ),
        _buildMetricCard(
          context,
          'Out of Stock',
          analytics.outOfStockProducts.toString(),
          Icons.warning,
          Colors.orange,
        ),
        _buildMetricCard(
          context,
          'Average Rating',
          analytics.averageRating.toStringAsFixed(1),
          Icons.star,
          Colors.amber,
        ),
        _buildMetricCard(
          context,
          'Categories',
          analytics.productsByCategory.length.toString(),
          Icons.category,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildMetricCard(BuildContext context, String title, String value,
      IconData icon, Color color) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopProducts(BuildContext context, ProductAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Top Performing Products',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.topProducts.map((product) => ListTile(
                  title: Text(product.name),
                  subtitle: Text(
                      'Sold: ${product.quantity} • Revenue: ${CurrencyFormatter.format(product.revenue)}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.star, color: Colors.amber, size: 16),
                      const SizedBox(width: 4),
                      Text(product.rating.toStringAsFixed(1)),
                    ],
                  ),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildLowStockProducts(
      BuildContext context, ProductAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Low Stock Products',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.lowStockProducts.map((product) => ListTile(
                  title: Text(product.name),
                  subtitle: Text('Quantity: ${product.quantity}'),
                  trailing: const Icon(Icons.warning, color: Colors.orange),
                )),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDistribution(
      BuildContext context, ProductAnalytics analytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Products by Category',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            ...analytics.productsByCategory.entries.map((entry) {
              final percentage = (entry.value /
                      analytics.productsByCategory.values
                          .reduce((a, b) => a + b) *
                      100)
                  .toStringAsFixed(1);
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(entry.key),
                        Text('$percentage% (${entry.value})'),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: entry.value /
                          analytics.productsByCategory.values
                              .reduce((a, b) => a + b),
                      backgroundColor: Colors.grey[200],
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.blue.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shivish/apps/seller/domain/models/campaign_model.dart';
import 'package:shivish/apps/seller/presentation/cubits/marketing_cubit.dart';
import 'package:shivish/apps/seller/screens/marketing/campaign_form.dart';
import 'package:shivish/shared/utils/date_formatter.dart';
import 'package:shivish/shared/utils/currency_formatter.dart';

class CampaignsTab extends StatelessWidget {
  const CampaignsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MarketingCubit, MarketingState>(
      builder: (context, state) {
        if (state.campaigns.isEmpty) {
          return const Center(
            child: Text('No campaigns found. Create one to get started!'),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: state.campaigns.length,
          itemBuilder: (context, index) {
            final campaign = state.campaigns[index];
            return _CampaignCard(campaign: campaign);
          },
        );
      },
    );
  }
}

class _CampaignCard extends StatelessWidget {
  final CampaignModel campaign;

  const _CampaignCard({required this.campaign});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    campaign.name,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                _buildStatusChip(context),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              campaign.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            _buildCampaignInfo(context),
            const SizedBox(height: 8),
            _buildDateRange(context),
            const SizedBox(height: 16),
            _buildMetrics(context),
            const SizedBox(height: 16),
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final color = _getStatusColor(campaign.status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _formatStatus(campaign.status),
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Color _getStatusColor(CampaignStatus status) {
    switch (status) {
      case CampaignStatus.draft:
        return Colors.grey;
      case CampaignStatus.scheduled:
        return Colors.orange;
      case CampaignStatus.active:
        return Colors.green;
      case CampaignStatus.paused:
        return Colors.amber;
      case CampaignStatus.completed:
        return Colors.blue;
      case CampaignStatus.cancelled:
        return Colors.red;
    }
  }

  String _formatStatus(CampaignStatus status) {
    return status.toString().split('.').last.toUpperCase();
  }

  Widget _buildCampaignInfo(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Text(
            _formatCampaignType(campaign.type),
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          'Budget: ${CurrencyFormatter.format(campaign.budget)}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  String _formatCampaignType(CampaignType type) {
    switch (type) {
      case CampaignType.productPromotion:
        return 'Product Promotion';
      case CampaignType.storePromotion:
        return 'Store Promotion';
      case CampaignType.seasonalSale:
        return 'Seasonal Sale';
      case CampaignType.flashSale:
        return 'Flash Sale';
    }
  }

  Widget _buildDateRange(BuildContext context) {
    return Row(
      children: [
        const Icon(Icons.calendar_today, size: 16),
        const SizedBox(width: 8),
        Text(
          '${DateFormatter.formatDate(campaign.startDate)} - ${DateFormatter.formatDate(campaign.endDate)}',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildMetrics(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildMetricItem(
          context,
          'Impressions',
          campaign.impressions.toString(),
          Icons.visibility,
        ),
        _buildMetricItem(
          context,
          'Clicks',
          campaign.clicks.toString(),
          Icons.touch_app,
        ),
        _buildMetricItem(
          context,
          'Conversions',
          campaign.conversions.toString(),
          Icons.shopping_cart,
        ),
        _buildMetricItem(
          context,
          'Spent',
          CurrencyFormatter.format(campaign.spent),
          Icons.attach_money,
        ),
      ],
    );
  }

  Widget _buildMetricItem(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(icon, size: 16, color: Theme.of(context).primaryColor),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall,
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        TextButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => CampaignForm(campaignId: campaign.id),
            );
          },
          child: const Text('Edit'),
        ),
        const SizedBox(width: 8),
        TextButton(
          onPressed: () {
            _showStatusUpdateDialog(context);
          },
          child: const Text('Update Status'),
        ),
        const SizedBox(width: 8),
        TextButton(
          onPressed: () {
            // Show delete confirmation dialog
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Delete Campaign'),
                content: const Text(
                  'Are you sure you want to delete this campaign? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () {
                      context
                          .read<MarketingCubit>()
                          .deleteCampaign(campaign.id);
                      Navigator.of(context).pop();
                    },
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            );
          },
          child: const Text(
            'Delete',
            style: TextStyle(color: Colors.red),
          ),
        ),
      ],
    );
  }

  void _showStatusUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Campaign Status'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: CampaignStatus.values.map((status) {
            return ListTile(
              title: Text(_formatStatus(status)),
              leading: Icon(
                Icons.circle,
                color: _getStatusColor(status),
                size: 12,
              ),
              onTap: () {
                context.read<MarketingCubit>().updateCampaignStatus(
                      campaign.id,
                      status,
                    );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }
}

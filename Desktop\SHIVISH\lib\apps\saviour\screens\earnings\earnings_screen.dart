import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../shared/utils/logger.dart';
import '../../models/earnings.dart';
import '../../providers/earnings_provider.dart';
import 'payout_request_screen.dart';
import 'earnings_history_screen.dart';

final _logger = getLogger('EarningsScreen');

class EarningsScreen extends ConsumerStatefulWidget {
  const EarningsScreen({super.key});

  @override
  ConsumerState<EarningsScreen> createState() => _EarningsScreenState();
}

class _EarningsScreenState extends ConsumerState<EarningsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _selectedPeriod = 'week';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Map<String, dynamic> _getDateRangeForPeriod(String period) {
    final now = DateTime.now();
    late DateTime startDate;

    switch (period) {
      case 'today':
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case 'week':
        // Start from the beginning of the week (Monday)
        final daysToSubtract = now.weekday - 1;
        startDate = DateTime(now.year, now.month, now.day - daysToSubtract);
        break;
      case 'month':
        startDate = DateTime(now.year, now.month, 1);
        break;
      default:
        startDate = DateTime(now.year, now.month, now.day - 6);
    }

    return {
      'startDate': startDate,
      'endDate': now,
    };
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final dateRange = _getDateRangeForPeriod(_selectedPeriod);

    final earningsSummaryAsync = ref.watch(earningsSummaryProvider(dateRange));
    final earningsStatisticsAsync = ref.watch(earningsStatisticsProvider);
    final dailyEarningsAsync = ref.watch(dailyEarningsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Earnings'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // Use pop() to go back to previous screen
            context.pop();
          },
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'History'),
            Tab(text: 'Payouts'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Overview tab
          _buildOverviewTab(
            theme,
            earningsSummaryAsync,
            earningsStatisticsAsync,
            dailyEarningsAsync,
          ),

          // History tab
          const EarningsHistoryScreen(),

          // Payouts tab
          _buildPayoutsTab(theme, earningsStatisticsAsync),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(
    ThemeData theme,
    AsyncValue<EarningsSummary> earningsSummaryAsync,
    AsyncValue<Map<String, dynamic>> earningsStatisticsAsync,
    AsyncValue<List<Map<String, dynamic>>> dailyEarningsAsync,
  ) {
    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(earningsSummaryProvider);
        ref.invalidate(earningsStatisticsProvider);
        ref.invalidate(dailyEarningsProvider);
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Period selector
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Period',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        _buildPeriodButton(theme, 'Today', 'today'),
                        const SizedBox(width: 8),
                        _buildPeriodButton(theme, 'This Week', 'week'),
                        const SizedBox(width: 8),
                        _buildPeriodButton(theme, 'This Month', 'month'),
                      ],
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            // Earnings summary
            earningsSummaryAsync.when(
              data: (summary) => _buildEarningsSummaryCard(theme, summary),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) {
                _logger.severe('Error loading earnings summary: $error\n$stackTrace');
                return Center(
                  child: Text('Error loading earnings summary: $error'),
                );
              },
            ),

            const SizedBox(height: 16),

            // Earnings chart
            dailyEarningsAsync.when(
              data: (dailyEarnings) => _buildEarningsChartCard(theme, dailyEarnings),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) {
                _logger.severe('Error loading daily earnings: $error\n$stackTrace');
                return Center(
                  child: Text('Error loading daily earnings: $error'),
                );
              },
            ),

            const SizedBox(height: 16),

            // Earnings statistics
            earningsStatisticsAsync.when(
              data: (statistics) => _buildEarningsStatisticsCard(theme, statistics),
              loading: () => const Center(child: CircularProgressIndicator()),
              error: (error, stackTrace) {
                _logger.severe('Error loading earnings statistics: $error\n$stackTrace');
                return Center(
                  child: Text('Error loading earnings statistics: $error'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodButton(ThemeData theme, String label, String period) {
    final isSelected = _selectedPeriod == period;

    return Expanded(
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedPeriod = period;
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? theme.colorScheme.primary : Colors.grey.shade200,
          foregroundColor: isSelected ? Colors.white : Colors.black,
          padding: const EdgeInsets.symmetric(vertical: 12),
        ),
        child: Text(label),
      ),
    );
  }

  Widget _buildEarningsSummaryCard(ThemeData theme, EarningsSummary summary) {
    final dateFormat = DateFormat('MMM d');
    final startDateStr = dateFormat.format(summary.startDate);
    final endDateStr = dateFormat.format(summary.endDate);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Earnings Summary',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  '$startDateStr - $endDateStr',
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Total Earnings',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${summary.totalEarnings.toStringAsFixed(2)}',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 40,
                  width: 1,
                  color: Colors.grey.shade300,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Deliveries',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${summary.completedDeliveriesCount}',
                        style: theme.textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Pending',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${summary.pendingPayouts.toStringAsFixed(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 40,
                  width: 1,
                  color: Colors.grey.shade300,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Paid',
                        style: theme.textTheme.bodyMedium,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '₹${summary.paidAmount.toStringAsFixed(2)}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsChartCard(ThemeData theme, List<Map<String, dynamic>> dailyEarnings) {
    // Format data for the chart
    final spots = <FlSpot>[];
    final dates = <String>[];
    double maxY = 0;

    for (int i = 0; i < dailyEarnings.length; i++) {
      final earning = dailyEarnings[i];
      final amount = earning['amount'] as double;
      spots.add(FlSpot(i.toDouble(), amount));

      if (amount > maxY) {
        maxY = amount;
      }

      // Format date for display
      final dateStr = earning['date'] as String;
      final date = DateTime.parse(dateStr);
      dates.add(DateFormat('E').format(date)); // Day of week abbreviation
    }

    // Ensure maxY is at least 100 for better visualization
    maxY = maxY < 100 ? 100 : maxY;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Earnings Trend',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              height: 200,
              child: spots.isEmpty
                  ? const Center(child: Text('No earnings data available'))
                  : LineChart(
                      LineChartData(
                        gridData: FlGridData(
                          show: true,
                          drawVerticalLine: false,
                          horizontalInterval: maxY / 5,
                          getDrawingHorizontalLine: (value) {
                            return FlLine(
                              color: Colors.grey.shade300,
                              strokeWidth: 1,
                            );
                          },
                        ),
                        titlesData: FlTitlesData(
                          show: true,
                          rightTitles: AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          topTitles: AxisTitles(
                            sideTitles: SideTitles(showTitles: false),
                          ),
                          bottomTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              getTitlesWidget: (value, meta) {
                                if (value.toInt() >= 0 && value.toInt() < dates.length) {
                                  return Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Text(
                                      dates[value.toInt()],
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                      ),
                                    ),
                                  );
                                }
                                return const SizedBox.shrink();
                              },
                              reservedSize: 30,
                            ),
                          ),
                          leftTitles: AxisTitles(
                            sideTitles: SideTitles(
                              showTitles: true,
                              interval: maxY / 5,
                              getTitlesWidget: (value, meta) {
                                return Padding(
                                  padding: const EdgeInsets.only(right: 8.0),
                                  child: Text(
                                    '₹${value.toInt()}',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey,
                                    ),
                                  ),
                                );
                              },
                              reservedSize: 40,
                            ),
                          ),
                        ),
                        borderData: FlBorderData(
                          show: false,
                        ),
                        minX: 0,
                        maxX: (spots.length - 1).toDouble(),
                        minY: 0,
                        maxY: maxY * 1.2, // Add some padding at the top
                        lineBarsData: [
                          LineChartBarData(
                            spots: spots,
                            isCurved: true,
                            color: theme.colorScheme.primary,
                            barWidth: 3,
                            isStrokeCapRound: true,
                            dotData: FlDotData(show: true),
                            belowBarData: BarAreaData(
                              show: true,
                              color: theme.colorScheme.primary.withOpacity(0.2),
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEarningsStatisticsCard(ThemeData theme, Map<String, dynamic> statistics) {
    final totalEarnings = statistics['totalEarnings'] as double;
    final pendingPayouts = statistics['pendingPayouts'] as double;
    final paidAmount = statistics['paidAmount'] as double;
    final totalDeliveries = statistics['totalDeliveries'] as int;
    final averageEarningsPerDelivery = statistics['averageEarningsPerDelivery'] as double;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Earnings Statistics',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatisticRow(
              theme,
              'Total Earnings',
              '₹${totalEarnings.toStringAsFixed(2)}',
              Icons.account_balance_wallet,
              theme.colorScheme.primary,
            ),
            const Divider(),
            _buildStatisticRow(
              theme,
              'Pending Payouts',
              '₹${pendingPayouts.toStringAsFixed(2)}',
              Icons.pending_actions,
              Colors.orange,
            ),
            const Divider(),
            _buildStatisticRow(
              theme,
              'Paid Amount',
              '₹${paidAmount.toStringAsFixed(2)}',
              Icons.check_circle,
              Colors.green,
            ),
            const Divider(),
            _buildStatisticRow(
              theme,
              'Total Deliveries',
              '$totalDeliveries',
              Icons.delivery_dining,
              Colors.purple,
            ),
            const Divider(),
            _buildStatisticRow(
              theme,
              'Average Per Delivery',
              '₹${averageEarningsPerDelivery.toStringAsFixed(2)}',
              Icons.trending_up,
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticRow(
    ThemeData theme,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPayoutsTab(
    ThemeData theme,
    AsyncValue<Map<String, dynamic>> earningsStatisticsAsync,
  ) {
    return earningsStatisticsAsync.when(
      data: (statistics) {
        final pendingPayouts = statistics['pendingPayouts'] as double;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Available balance card
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Available Balance',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Center(
                        child: Text(
                          '₹${pendingPayouts.toStringAsFixed(2)}',
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      FilledButton(
                        onPressed: pendingPayouts > 0
                            ? () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => PayoutRequestScreen(
                                      availableBalance: pendingPayouts,
                                    ),
                                  ),
                                );
                              }
                            : null,
                        style: FilledButton.styleFrom(
                          minimumSize: const Size(double.infinity, 48),
                        ),
                        child: const Text('Request Payout'),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Payout history
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Payout History',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              // Navigate to payout history screen
                            },
                            child: const Text('View All'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      _buildPayoutsListView(),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Payout methods
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Payout Methods',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildPayoutMethodItem(
                        theme,
                        'Bank Transfer',
                        'Receive funds directly in your bank account',
                        Icons.account_balance,
                        Colors.blue,
                      ),
                      const Divider(),
                      _buildPayoutMethodItem(
                        theme,
                        'UPI',
                        'Instant transfer to your UPI ID',
                        Icons.phone_android,
                        Colors.green,
                      ),
                      const Divider(),
                      _buildPayoutMethodItem(
                        theme,
                        'Wallet',
                        'Transfer to your digital wallet',
                        Icons.account_balance_wallet,
                        Colors.orange,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        _logger.severe('Error loading earnings statistics: $error\n$stackTrace');
        return Center(
          child: Text('Error loading earnings statistics: $error'),
        );
      },
    );
  }

  Widget _buildPayoutsListView() {
    final payoutsAsync = ref.watch(payoutsListProvider({
      'startDate': null,
      'endDate': null,
      'status': null,
    }));

    return payoutsAsync.when(
      data: (payouts) {
        if (payouts.isEmpty) {
          return const Padding(
            padding: EdgeInsets.symmetric(vertical: 16),
            child: Center(
              child: Text('No payout history available'),
            ),
          );
        }

        // Show only the last 3 payouts
        final recentPayouts = payouts.take(3).toList();

        return ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: recentPayouts.length,
          itemBuilder: (context, index) {
            final payout = recentPayouts[index];
            final amount = payout['amount'] as double;
            final status = payout['status'] as String;
            final timestamp = payout['timestamp'] is Map
                ? DateTime.fromMillisecondsSinceEpoch(payout['timestamp']['seconds'] * 1000)
                : DateTime.now();
            final paymentMethod = payout['paymentMethod'] as String;

            return ListTile(
              contentPadding: EdgeInsets.zero,
              title: Text(
                '₹${amount.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                '${DateFormat('MMM d, yyyy').format(timestamp)} • $paymentMethod',
              ),
              trailing: _buildPayoutStatusChip(status),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) {
        _logger.severe('Error loading payouts: $error\n$stackTrace');
        return Center(
          child: Text('Error loading payouts: $error'),
        );
      },
    );
  }

  Widget _buildPayoutStatusChip(String status) {
    late Color color;
    late String label;

    switch (status.toLowerCase()) {
      case 'pending':
        color = Colors.orange;
        label = 'Pending';
        break;
      case 'processing':
        color = Colors.blue;
        label = 'Processing';
        break;
      case 'completed':
      case 'paid':
        color = Colors.green;
        label = 'Completed';
        break;
      case 'failed':
        color = Colors.red;
        label = 'Failed';
        break;
      default:
        color = Colors.grey;
        label = status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Text(
        label,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPayoutMethodItem(
    ThemeData theme,
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ),
          ),
          Radio<String>(
            value: title,
            groupValue: 'Bank Transfer', // Default selected method
            onChanged: (value) {
              // Handle method selection
            },
          ),
        ],
      ),
    );
  }
}
